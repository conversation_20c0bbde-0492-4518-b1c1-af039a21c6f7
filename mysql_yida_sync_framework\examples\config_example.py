# -*- coding: utf-8 -*-
"""
配置示例

展示如何创建和保存配置文件。
"""

import json
import os
from mysql_yida_sync_framework import SyncConfig


def create_sales_config_example():
    """创建销售数据同步配置示例"""
    
    # 数据库配置
    database_config = {
        'host': 'your_mysql_host',
        'port': 3306,
        'user': 'your_username',
        'password': 'your_password',
        'database': 'your_database',
        'charset': 'utf8mb4',
        'cursorclass': 'DictCursor'
    }
    
    # 宜搭配置
    yida_config = {
        'app_type': 'your_app_type',
        'system_token': 'your_system_token',
        'user_id': 'your_user_id',
        'language': 'zh_CN',
        'form_uuid': 'your_form_uuid'
    }
    
    # 字段映射
    field_mapping = {
        'project_code': 'textField_xxx',
        'project_name': 'textField_xxx', 
        'store_code': 'textField_xxx',
        'store_name': 'textField_xxx',
        'sale_date': 'dateField_xxx',
        'online_amount': 'numberField_xxx',
        'offline_amount': 'numberField_xxx', 
        'total_amount': 'numberField_xxx',
        'order_count': 'numberField_xxx',
        'report_source': 'textField_xxx',
        'url': 'textField_xxx'
    }
    
    # 需要比较的字段
    compare_fields = [
        'online_amount',
        'offline_amount',
        'total_amount',
        'order_count',
        'report_source',
        'url',
        'store_name'
    ]
    
    # 主键字段
    primary_key_fields = ['project_code', 'store_code', 'sale_date']
    
    # SQL查询
    sql_query = """
        SELECT 
            a.project_code,
            b.name AS project_name,
            a.store_code,
            c.name AS store_name,
            DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sale_date,
            a.online_amount AS online_amount,
            a.offline_amount AS offline_amount,
            a.total_amount AS total_amount,
            a.order_count AS order_count,
            a.report_source,
            d.url
        FROM
            sales_record a
                JOIN
            projects b ON a.project_code = b.code
                JOIN
            stores c ON a.store_code = c.code
                LEFT JOIN
            attachments d ON a.id = d.record_id
        WHERE
            a.status = 1 AND a.deleted = 0
    """
    
    # 创建配置对象
    config = SyncConfig(
        name="sales_sync_example",
        description="销售数据同步配置示例",
        database=database_config,
        yida=yida_config,
        field_mapping=field_mapping,
        compare_fields=compare_fields,
        primary_key_fields=primary_key_fields,
        date_field='sale_date',
        date_format='%Y-%m-%d',
        sql_query=sql_query,
        sync_strategy='incremental',
        batch_size=50,
        page_size=50,
        log_level='INFO',
        max_retries=3,
        retry_delay=5
    )
    
    return config


def create_user_config_example():
    """创建用户数据同步配置示例"""
    
    # 数据库配置
    database_config = {
        'host': 'your_mysql_host',
        'port': 3306,
        'user': 'your_username',
        'password': 'your_password',
        'database': 'your_database',
        'charset': 'utf8mb4',
        'cursorclass': 'DictCursor'
    }
    
    # 宜搭配置
    yida_config = {
        'app_type': 'your_app_type',
        'system_token': 'your_system_token',
        'user_id': 'your_user_id',
        'language': 'zh_CN',
        'form_uuid': 'your_form_uuid'
    }
    
    # 字段映射
    field_mapping = {
        'user_id': 'textField_xxx',
        'username': 'textField_xxx',
        'email': 'textField_xxx',
        'phone': 'textField_xxx',
        'department': 'textField_xxx',
        'position': 'textField_xxx',
        'hire_date': 'dateField_xxx',
        'salary': 'numberField_xxx',
        'status': 'selectField_xxx'
    }
    
    # 需要比较的字段
    compare_fields = [
        'username',
        'email',
        'phone',
        'department',
        'position',
        'salary',
        'status'
    ]
    
    # 主键字段
    primary_key_fields = ['user_id']
    
    # SQL查询
    sql_query = """
        SELECT 
            u.id as user_id,
            u.username,
            u.email,
            u.phone,
            d.name as department,
            p.name as position,
            DATE_FORMAT(u.hire_date, '%%Y-%%m-%%d') as hire_date,
            u.salary,
            u.status
        FROM
            users u
                LEFT JOIN
            departments d ON u.department_id = d.id
                LEFT JOIN
            positions p ON u.position_id = p.id
        WHERE
            u.deleted = 0
    """
    
    # 创建配置对象
    config = SyncConfig(
        name="user_sync_example",
        description="用户数据同步配置示例",
        database=database_config,
        yida=yida_config,
        field_mapping=field_mapping,
        compare_fields=compare_fields,
        primary_key_fields=primary_key_fields,
        date_field='hire_date',
        date_format='%Y-%m-%d',
        sql_query=sql_query,
        sync_strategy='incremental',
        batch_size=30,
        page_size=30,
        log_level='INFO',
        max_retries=3,
        retry_delay=5
    )
    
    return config


def create_order_config_example():
    """创建订单数据同步配置示例"""
    
    # 数据库配置
    database_config = {
        'host': 'your_mysql_host',
        'port': 3306,
        'user': 'your_username',
        'password': 'your_password',
        'database': 'your_database',
        'charset': 'utf8mb4',
        'cursorclass': 'DictCursor'
    }
    
    # 宜搭配置
    yida_config = {
        'app_type': 'your_app_type',
        'system_token': 'your_system_token',
        'user_id': 'your_user_id',
        'language': 'zh_CN',
        'form_uuid': 'your_form_uuid'
    }
    
    # 字段映射
    field_mapping = {
        'order_id': 'textField_xxx',
        'customer_name': 'textField_xxx',
        'product_name': 'textField_xxx',
        'order_date': 'dateField_xxx',
        'quantity': 'numberField_xxx',
        'unit_price': 'numberField_xxx',
        'total_amount': 'numberField_xxx',
        'order_status': 'selectField_xxx',
        'payment_method': 'textField_xxx'
    }
    
    # 需要比较的字段
    compare_fields = [
        'customer_name',
        'product_name',
        'quantity',
        'unit_price',
        'total_amount',
        'order_status',
        'payment_method'
    ]
    
    # 主键字段
    primary_key_fields = ['order_id']
    
    # SQL查询
    sql_query = """
        SELECT 
            o.id as order_id,
            c.name as customer_name,
            p.name as product_name,
            DATE_FORMAT(o.order_date, '%%Y-%%m-%%d') as order_date,
            o.quantity,
            o.unit_price,
            o.total_amount,
            o.status as order_status,
            o.payment_method
        FROM
            orders o
                JOIN
            customers c ON o.customer_id = c.id
                JOIN
            products p ON o.product_id = p.id
        WHERE
            o.deleted = 0
    """
    
    # 创建配置对象
    config = SyncConfig(
        name="order_sync_example",
        description="订单数据同步配置示例",
        database=database_config,
        yida=yida_config,
        field_mapping=field_mapping,
        compare_fields=compare_fields,
        primary_key_fields=primary_key_fields,
        date_field='order_date',
        date_format='%Y-%m-%d',
        sql_query=sql_query,
        sync_strategy='incremental',
        batch_size=100,
        page_size=100,
        log_level='INFO',
        max_retries=3,
        retry_delay=5
    )
    
    return config


def save_config_to_file(config: SyncConfig, filename: str):
    """
    保存配置到文件
    
    Args:
        config: 配置对象
        filename: 文件名
    """
    try:
        # 确保目录存在
        os.makedirs('configs', exist_ok=True)
        
        # 保存配置
        config.save_to_json_file(f"configs/{filename}")
        print(f"配置已保存到: configs/{filename}")
        
    except Exception as e:
        print(f"保存配置失败: {str(e)}")


def load_config_from_file(filename: str) -> SyncConfig:
    """
    从文件加载配置
    
    Args:
        filename: 文件名
        
    Returns:
        SyncConfig: 配置对象
    """
    try:
        config = SyncConfig.from_json_file(f"configs/{filename}")
        print(f"配置已从文件加载: configs/{filename}")
        return config
        
    except Exception as e:
        print(f"加载配置失败: {str(e)}")
        return None


def main():
    """主函数"""
    print("=== MySQL到宜搭同步框架配置示例 ===\n")
    
    # 创建配置示例
    print("1. 创建销售数据同步配置示例")
    sales_config = create_sales_config_example()
    save_config_to_file(sales_config, "sales_sync_config.json")
    
    print("\n2. 创建用户数据同步配置示例")
    user_config = create_user_config_example()
    save_config_to_file(user_config, "user_sync_config.json")
    
    print("\n3. 创建订单数据同步配置示例")
    order_config = create_order_config_example()
    save_config_to_file(order_config, "order_sync_config.json")
    
    # 验证配置
    print("\n4. 验证配置")
    configs = [sales_config, user_config, order_config]
    
    for config in configs:
        print(f"\n验证配置: {config.name}")
        errors = config.validate()
        if errors:
            print("配置错误:")
            for error in errors:
                print(f"  - {error}")
        else:
            print("配置验证通过")
    
    # 加载配置示例
    print("\n5. 加载配置示例")
    loaded_config = load_config_from_file("sales_sync_config.json")
    if loaded_config:
        print(f"加载的配置名称: {loaded_config.name}")
        print(f"配置描述: {loaded_config.description}")
    
    print("\n=== 配置示例完成 ===")
    print("\n使用说明:")
    print("1. 修改配置文件中的连接信息")
    print("2. 根据实际宜搭表单调整字段映射")
    print("3. 根据实际数据库表调整SQL查询")
    print("4. 使用SyncClient执行同步")


if __name__ == '__main__':
    main() 