# -*- coding: utf-8 -*-
"""
日志工具类

提供统一的日志管理功能。
"""

import logging
import os
from datetime import datetime, timedelta
from typing import Optional


class Logger:
    """日志管理器"""
    
    _loggers = {}
    
    @classmethod
    def get_logger(cls, log_file: str = None, log_level: str = "INFO") -> logging.Logger:
        """
        获取日志记录器
        
        Args:
            log_file: 日志文件路径
            log_level: 日志级别
            
        Returns:
            logging.Logger: 日志记录器
        """
        # 生成日志器名称
        logger_name = f"mysql_yida_sync_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        # 如果已经存在，直接返回
        if logger_name in cls._loggers:
            return cls._loggers[logger_name]
        
        # 创建日志记录器
        logger = logging.getLogger(logger_name)
        logger.setLevel(getattr(logging, log_level.upper(), logging.INFO))
        
        # 清除已有的处理器
        logger.handlers.clear()
        
        # 创建格式化器
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        # 创建控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)
        
        # 创建文件处理器
        if log_file:
            try:
                # 确保日志目录存在
                log_dir = os.path.dirname(log_file)
                if log_dir and not os.path.exists(log_dir):
                    os.makedirs(log_dir, exist_ok=True)
                
                file_handler = logging.FileHandler(log_file, encoding='utf-8')
                file_handler.setLevel(getattr(logging, log_level.upper(), logging.INFO))
                file_handler.setFormatter(formatter)
                logger.addHandler(file_handler)
                
            except Exception as e:
                print(f"创建日志文件处理器失败: {str(e)}")
        
        # 存储日志记录器
        cls._loggers[logger_name] = logger
        
        return logger
    
    @classmethod
    def setup_logging(cls, config_name: str, log_level: str = "INFO") -> logging.Logger:
        """
        设置日志记录
        
        Args:
            config_name: 配置名称
            log_level: 日志级别
            
        Returns:
            logging.Logger: 日志记录器
        """
        # 生成日志文件路径
        current_date = datetime.now().strftime('%Y%m%d')
        log_file = f"logs/sync_{config_name}_log_{current_date}.txt"
        
        return cls.get_logger(log_file, log_level)
    
    @classmethod
    def get_log_file_path(cls, config_name: str) -> str:
        """
        获取日志文件路径
        
        Args:
            config_name: 配置名称
            
        Returns:
            str: 日志文件路径
        """
        current_date = datetime.now().strftime('%Y%m%d')
        return f"logs/sync_{config_name}_log_{current_date}.txt"
    
    @classmethod
    def cleanup_old_logs(cls, days: int = 30):
        """
        清理旧日志文件
        
        Args:
            days: 保留天数
        """
        try:
            log_dir = "logs"
            if not os.path.exists(log_dir):
                return
            
            cutoff_date = datetime.now() - timedelta(days=days)
            
            for filename in os.listdir(log_dir):
                if filename.endswith('.txt') and filename.startswith('sync_'):
                    file_path = os.path.join(log_dir, filename)
                    file_time = datetime.fromtimestamp(os.path.getctime(file_path))
                    
                    if file_time < cutoff_date:
                        try:
                            os.remove(file_path)
                            print(f"已删除旧日志文件: {filename}")
                        except Exception as e:
                            print(f"删除日志文件失败: {filename}, 错误: {str(e)}")
                            
        except Exception as e:
            print(f"清理旧日志文件失败: {str(e)}") 