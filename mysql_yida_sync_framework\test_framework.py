# -*- coding: utf-8 -*-
"""
框架测试脚本

用于验证MySQL到宜搭同步框架的基本功能。
"""

import sys
import os
from datetime import datetime

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """测试模块导入"""
    print("=== 测试模块导入 ===")
    
    try:
        from mysql_yida_sync_framework import SyncConfig, SyncClient, FieldMapper, DataProcessor
        print("✓ 核心模块导入成功")
    except ImportError as e:
        print(f"✗ 核心模块导入失败: {e}")
        return False
    
    try:
        from mysql_yida_sync_framework.clients import YidaClient, MySQLClient
        print("✓ 客户端模块导入成功")
    except ImportError as e:
        print(f"✗ 客户端模块导入失败: {e}")
        return False
    
    try:
        from mysql_yida_sync_framework.utils import Logger, Validators
        print("✓ 工具模块导入成功")
    except ImportError as e:
        print(f"✗ 工具模块导入失败: {e}")
        return False
    
    return True


def test_config_creation():
    """测试配置创建"""
    print("\n=== 测试配置创建 ===")
    
    try:
        from mysql_yida_sync_framework import SyncConfig
        
        # 创建测试配置
        config = SyncConfig(
            name="test_config",
            description="测试配置",
            database={
                'host': 'test_host',
                'user': 'test_user',
                'database': 'test_db'
            },
            yida={
                'app_type': 'test_app',
                'system_token': 'test_token',
                'user_id': 'test_user',
                'form_uuid': 'test_form'
            },
            field_mapping={
                'test_field': 'textField_test'
            },
            compare_fields=['test_field'],
            primary_key_fields=['test_field'],
            sql_query="SELECT * FROM test_table"
        )
        
        print("✓ 配置创建成功")
        print(f"  配置名称: {config.name}")
        print(f"  字段映射数量: {len(config.field_mapping)}")
        
        # 测试配置验证
        errors = config.validate()
        if errors:
            print(f"✗ 配置验证失败: {errors}")
            return False
        else:
            print("✓ 配置验证通过")
        
        # 测试配置序列化
        config_dict = config.to_dict()
        print("✓ 配置序列化成功")
        
        # 测试配置反序列化
        new_config = SyncConfig.from_dict(config_dict)
        print("✓ 配置反序列化成功")
        
        return True
        
    except Exception as e:
        print(f"✗ 配置创建失败: {e}")
        return False


def test_field_mapper():
    """测试字段映射器"""
    print("\n=== 测试字段映射器 ===")
    
    try:
        from mysql_yida_sync_framework import FieldMapper
        
        field_mapping = {
            'project_code': 'textField_xxx',
            'amount': 'numberField_xxx',
            'date': 'dateField_xxx'
        }
        compare_fields = ['amount', 'project_code']
        
        mapper = FieldMapper(field_mapping, compare_fields)
        
        print("✓ 字段映射器创建成功")
        print(f"  字段类型检测: {mapper.field_types}")
        
        # 测试字段映射
        yida_field = mapper.get_yida_field('project_code')
        print(f"✓ 字段映射测试: project_code -> {yida_field}")
        
        # 测试数据转换
        test_data = {
            'project_code': 'TEST001',
            'amount': '100.50',
            'date': '2024-01-01'
        }
        
        yida_data = mapper.map_mysql_to_yida(test_data)
        print(f"✓ 数据转换测试: {yida_data}")
        
        # 测试字段映射验证
        errors = mapper.validate_mapping()
        if errors:
            print(f"✗ 字段映射验证失败: {errors}")
            return False
        else:
            print("✓ 字段映射验证通过")
        
        return True
        
    except Exception as e:
        print(f"✗ 字段映射器测试失败: {e}")
        return False


def test_data_processor():
    """测试数据处理器"""
    print("\n=== 测试数据处理器 ===")
    
    try:
        from mysql_yida_sync_framework import FieldMapper, DataProcessor
        from mysql_yida_sync_framework import SyncConfig
        
        # 创建测试配置
        config = SyncConfig(
            name="test_config",
            database={'host': 'test'},
            yida={'app_type': 'test', 'system_token': 'test', 'user_id': 'test', 'form_uuid': 'test'},
            field_mapping={'test_field': 'textField_test'},
            compare_fields=['test_field'],
            primary_key_fields=['test_field'],
            sql_query="SELECT * FROM test"
        )
        
        field_mapper = FieldMapper(config.field_mapping, config.compare_fields)
        processor = DataProcessor(field_mapper, config)
        
        print("✓ 数据处理器创建成功")
        
        # 测试数据处理
        test_data = [
            {'test_field': 'value1', 'amount': '100'},
            {'test_field': 'value2', 'amount': '200'}
        ]
        
        processed_data = processor.process_mysql_data(test_data)
        print(f"✓ 数据处理测试: 处理了 {len(processed_data)} 条记录")
        
        # 测试数据比较
        mysql_data = {'key1': {'test_field': 'value1', 'amount': '100'}}
        yida_data = {'key1': {'test_field': 'value1', 'amount': '100', '_form_instance_id': 'form1'}}
        
        update_list, insert_list, error_list = processor.compare_data(mysql_data, yida_data)
        print(f"✓ 数据比较测试: 更新 {len(update_list)} 条, 插入 {len(insert_list)} 条, 错误 {len(error_list)} 条")
        
        return True
        
    except Exception as e:
        print(f"✗ 数据处理器测试失败: {e}")
        return False


def test_validators():
    """测试数据验证器"""
    print("\n=== 测试数据验证器 ===")
    
    try:
        from mysql_yida_sync_framework.utils.validators import Validators
        
        # 测试邮箱验证
        assert Validators.validate_email("<EMAIL>") == True
        assert Validators.validate_email("invalid-email") == False
        print("✓ 邮箱验证测试通过")
        
        # 测试手机号验证
        assert Validators.validate_phone("13800138000") == True
        assert Validators.validate_phone("12345678901") == False
        print("✓ 手机号验证测试通过")
        
        # 测试日期验证
        assert Validators.validate_date("2024-01-01") == True
        assert Validators.validate_date("invalid-date") == False
        print("✓ 日期验证测试通过")
        
        # 测试数值验证
        assert Validators.validate_number("100.50") == True
        assert Validators.validate_number("invalid") == False
        print("✓ 数值验证测试通过")
        
        # 测试字符串清理
        cleaned = Validators.sanitize_string("  test  ")
        assert cleaned == "test"
        print("✓ 字符串清理测试通过")
        
        return True
        
    except Exception as e:
        print(f"✗ 数据验证器测试失败: {e}")
        return False


def test_logger():
    """测试日志工具"""
    print("\n=== 测试日志工具 ===")
    
    try:
        from mysql_yida_sync_framework.utils.logger import Logger
        
        # 测试日志器创建
        logger = Logger.get_logger()
        logger.info("测试日志消息")
        print("✓ 日志器创建成功")
        
        # 测试日志文件路径生成
        log_path = Logger.get_log_file_path("test_config")
        print(f"✓ 日志文件路径生成: {log_path}")
        
        return True
        
    except Exception as e:
        print(f"✗ 日志工具测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("MySQL到宜搭同步框架测试")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_config_creation,
        test_field_mapper,
        test_data_processor,
        test_validators,
        test_logger
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"✗ 测试执行异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！框架基本功能正常。")
        return 0
    else:
        print("❌ 部分测试失败，请检查相关模块。")
        return 1


if __name__ == '__main__':
    exit(main()) 