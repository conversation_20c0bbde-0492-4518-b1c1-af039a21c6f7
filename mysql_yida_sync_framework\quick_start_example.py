# -*- coding: utf-8 -*-
"""
快速开始示例

展示如何使用MySQL到宜搭同步框架进行数据同步。
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.sync_config import SyncConfig, DatabaseConfig, YidaConfig


class SimpleTokenProvider:
    """简单的Token提供者"""
    
    @staticmethod
    def get_token():
        """获取访问令牌"""
        return "dummy_token_for_testing"


def create_simple_config():
    """创建简单的同步配置"""
    
    # 数据库配置
    database_config = DatabaseConfig(
        host='your_mysql_host',
        port=3306,
        user='your_username',
        password='your_password',
        database='your_database',
        charset='utf8mb4',
        cursorclass='DictCursor'
    )
    
    # 宜搭配置
    yida_config = YidaConfig(
        app_type='your_app_type',
        system_token='your_system_token',
        user_id='your_user_id',
        language='zh_CN',
        form_uuid='your_form_uuid'
    )
    
    # 字段映射
    field_mapping = {
        'project_code': 'textField_xxx',
        'project_name': 'textField_xxx',
        'amount': 'numberField_xxx',
        'date': 'dateField_xxx'
    }
    
    # 创建配置对象
    config = SyncConfig(
        name="simple_sync",
        description="简单数据同步配置",
        database=database_config,
        yida=yida_config,
        field_mapping=field_mapping,
        compare_fields=['amount', 'project_name'],
        primary_key_fields=['project_code', 'date'],
        date_field='date',
        date_format='%Y-%m-%d',
        sql_query="SELECT project_code, project_name, amount, DATE_FORMAT(create_time, '%Y-%m-%d') as date FROM your_table WHERE status = 1",
        sync_strategy='incremental',
        batch_size=50,
        page_size=50
    )
    
    return config


def main():
    """主函数"""
    print("=== MySQL到宜搭同步框架快速开始示例 ===\n")
    
    try:
        # 1. 创建配置
        print("1. 创建同步配置...")
        config = create_simple_config()
        print(f"   配置名称: {config.name}")
        print(f"   字段映射数量: {len(config.field_mapping)}")
        
        # 2. 验证配置
        print("\n2. 验证配置...")
        errors = config.validate()
        if errors:
            print("   配置验证失败:")
            for error in errors:
                print(f"     - {error}")
            return
        print("   配置验证通过")
        
        # 3. 创建同步客户端
        print("\n3. 创建同步客户端...")
        from sync.sync_client import SyncClient
        token_provider = SimpleTokenProvider()
        sync_client = SyncClient(config, token_provider)
        print("   同步客户端创建成功")
        
        # 4. 测试连接（需要真实的连接信息）
        print("\n4. 测试连接...")
        print("   注意: 需要真实的数据库和宜搭连接信息才能进行连接测试")
        print("   请修改配置中的连接信息后重新运行")
        
        # 5. 获取系统信息
        print("\n5. 获取系统信息...")
        system_info = sync_client.get_system_info()
        print(f"   配置名称: {system_info['config']['name']}")
        print(f"   同步策略: {system_info['config']['sync_strategy']}")
        print(f"   字段映射数量: {system_info['field_mapping']['total_fields']}")
        
        print("\n=== 快速开始示例完成 ===")
        print("\n使用说明:")
        print("1. 修改配置中的数据库连接信息")
        print("2. 修改配置中的宜搭连接信息")
        print("3. 根据实际表单调整字段映射")
        print("4. 根据实际表结构调整SQL查询")
        print("5. 运行 sync_client.sync_data() 执行同步")
        
    except Exception as e:
        print(f"示例执行失败: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main() 