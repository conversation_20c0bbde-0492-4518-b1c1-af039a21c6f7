# -*- coding: utf-8 -*-
"""
同步客户端

整合所有组件，实现完整的数据同步功能。
"""

import logging
import time
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, timedelta

from core.sync_config import SyncConfig
from core.field_mapper import FieldMapper
from core.data_processor import DataProcessor
from clients.yida_client import YidaClient
from clients.mysql_client import MySQLClient
from utils.logger import Logger


class SyncClient:
    """数据同步客户端"""
    
    def __init__(self, config: SyncConfig, token_provider=None):
        """
        初始化同步客户端
        
        Args:
            config: 同步配置对象
            token_provider: Token提供者
        """
        self.config = config
        self.logger = Logger.get_logger(config.log_file, config.log_level)
        
        # 初始化组件
        self.field_mapper = FieldMapper(config.field_mapping, config.compare_fields)
        self.data_processor = DataProcessor(self.field_mapper, config)
        self.mysql_client = MySQLClient(config)
        self.yida_client = YidaClient(config, token_provider)
        
        # 验证配置
        self._validate_config()
        
        # 同步统计
        self.stats = {
            'total_update': 0,
            'total_insert': 0,
            'total_error': 0,
            'start_time': None,
            'end_time': None
        }
    
    def _validate_config(self):
        """验证配置"""
        # 验证基本配置
        errors = self.config.validate()
        if errors:
            raise ValueError(f"配置验证失败: {'; '.join(errors)}")
        
        # 验证字段映射
        mapping_errors = self.field_mapper.validate_mapping()
        if mapping_errors:
            raise ValueError(f"字段映射验证失败: {'; '.join(mapping_errors)}")
        
        # 验证SQL语句
        sql_errors = self.mysql_client.validate_sql(self.config.sql_query)
        if sql_errors:
            raise ValueError(f"SQL语句验证失败: {'; '.join(sql_errors)}")
        
        self.logger.info("配置验证通过")
    
    def test_connections(self) -> Dict[str, bool]:
        """
        测试所有连接
        
        Returns:
            Dict[str, bool]: 连接测试结果
        """
        results = {}
        
        # 测试MySQL连接
        try:
            results['mysql'] = self.mysql_client.test_connection()
        except Exception as e:
            self.logger.error(f"MySQL连接测试失败: {str(e)}")
            results['mysql'] = False
        
        # 测试宜搭连接
        try:
            results['yida'] = self.yida_client.test_connection()
        except Exception as e:
            self.logger.error(f"宜搭连接测试失败: {str(e)}")
            results['yida'] = False
        
        # 记录测试结果
        for name, result in results.items():
            status = "成功" if result else "失败"
            self.logger.info(f"{name}连接测试: {status}")
        
        return results
    
    def sync_data(self, start_date: Optional[str] = None, end_date: Optional[str] = None) -> Dict[str, Any]:
        """
        执行数据同步
        
        Args:
            start_date: 开始日期，格式：YYYY-MM-DD
            end_date: 结束日期，格式：YYYY-MM-DD
            
        Returns:
            Dict[str, Any]: 同步结果统计
        """
        self.stats['start_time'] = datetime.now()
        self.logger.info(f"开始数据同步 - 配置: {self.config.name}")
        
        try:
            # 测试连接
            connection_results = self.test_connections()
            if not all(connection_results.values()):
                failed_connections = [k for k, v in connection_results.items() if not v]
                raise Exception(f"连接测试失败: {', '.join(failed_connections)}")
            
            # 获取MySQL数据
            mysql_data = self.mysql_client.get_data(start_date, end_date)
            if not mysql_data:
                self.logger.warning("未获取到MySQL数据")
                return self._get_sync_result()
            
            # 处理MySQL数据
            processed_mysql_data = self.data_processor.process_mysql_data(mysql_data)
            
            # 按日期分组处理
            date_groups = self.data_processor.group_by_date(processed_mysql_data)
            self.logger.info(f"数据按日期分组完成，共 {len(date_groups)} 个日期")
            
            # 按日期循环处理
            for date, keys in date_groups.items():
                try:
                    self.logger.info(f"开始处理日期: {date}")
                    
                    # 构建查询条件
                    search_condition = self.data_processor.build_search_condition(date, self.config.date_format)
                    
                    # 获取宜搭数据
                    yida_data = self.yida_client.get_form_data(search_condition=search_condition)
                    
                    # 处理宜搭数据
                    processed_yida_data = self.data_processor.process_yida_data(yida_data)
                    
                    # 比较数据
                    update_list, insert_list, error_list = self.data_processor.compare_data(
                        {k: processed_mysql_data[k] for k in keys},
                        processed_yida_data
                    )
                    
                    # 执行更新
                    self._execute_updates(update_list)
                    
                    # 执行插入
                    self._execute_inserts(insert_list)
                    
                    # 记录错误
                    for error in error_list:
                        self.logger.error(f"日期 {date} 处理错误: {error}")
                        self.stats['total_error'] += 1
                    
                    self.logger.info(f"日期 {date} 处理完成 - 更新: {len(update_list)} 条，插入: {len(insert_list)} 条")
                    
                except Exception as e:
                    self.logger.error(f"处理日期 {date} 时发生错误: {str(e)}")
                    self.stats['total_error'] += 1
                    continue
            
            self.logger.info("数据同步完成")
            return self._get_sync_result()
            
        except Exception as e:
            self.logger.error(f"数据同步失败: {str(e)}")
            raise
        finally:
            self.stats['end_time'] = datetime.now()
            self._cleanup()
    
    def _execute_updates(self, update_list: List[Dict[str, Any]]):
        """执行更新操作"""
        if not update_list:
            return
        
        self.logger.info(f"开始执行 {len(update_list)} 条更新操作")
        
        for item in update_list:
            try:
                # 将MySQL数据映射为宜搭格式
                yida_data = self.field_mapper.map_mysql_to_yida(item['mysql_data'])
                
                # 执行更新
                success = self.yida_client.update_form_data(item['form_instance_id'], yida_data)
                
                if success:
                    self.stats['total_update'] += 1
                    self.logger.debug(f"更新成功: {item['key']}")
                else:
                    self.stats['total_error'] += 1
                    self.logger.error(f"更新失败: {item['key']}")
                
                # 添加延时避免请求过于频繁
                time.sleep(0.1)
                
            except Exception as e:
                self.logger.error(f"更新操作失败: {item['key']}, 错误: {str(e)}")
                self.stats['total_error'] += 1
    
    def _execute_inserts(self, insert_list: List[Dict[str, Any]]):
        """执行插入操作"""
        if not insert_list:
            return
        
        self.logger.info(f"开始执行 {len(insert_list)} 条插入操作")
        
        try:
            # 将MySQL数据映射为宜搭格式
            yida_data_list = []
            for item in insert_list:
                yida_data = self.field_mapper.map_mysql_to_yida(item['mysql_data'])
                yida_data_list.append(yida_data)
            
            # 批量插入
            success = self.yida_client.batch_create_form_data(yida_data_list, self.config.batch_size)
            
            if success:
                self.stats['total_insert'] += len(insert_list)
                self.logger.info(f"批量插入成功: {len(insert_list)} 条记录")
            else:
                self.stats['total_error'] += len(insert_list)
                self.logger.error(f"批量插入失败: {len(insert_list)} 条记录")
                
        except Exception as e:
            self.logger.error(f"插入操作失败: {str(e)}")
            self.stats['total_error'] += len(insert_list)
    
    def _get_sync_result(self) -> Dict[str, Any]:
        """获取同步结果"""
        duration = None
        if self.stats['start_time'] and self.stats['end_time']:
            duration = (self.stats['end_time'] - self.stats['start_time']).total_seconds()
        
        return {
            'config_name': self.config.name,
            'start_time': self.stats['start_time'],
            'end_time': self.stats['end_time'],
            'duration_seconds': duration,
            'total_update': self.stats['total_update'],
            'total_insert': self.stats['total_insert'],
            'total_error': self.stats['total_error'],
            'success': self.stats['total_error'] == 0
        }
    
    def _cleanup(self):
        """清理资源"""
        try:
            self.mysql_client.disconnect()
        except Exception as e:
            self.logger.error(f"清理MySQL连接时出错: {str(e)}")
    
    def get_system_info(self) -> Dict[str, Any]:
        """
        获取系统信息
        
        Returns:
            Dict[str, Any]: 系统信息
        """
        info = {
            'config': {
                'name': self.config.name,
                'description': self.config.description,
                'sync_strategy': self.config.sync_strategy,
                'batch_size': self.config.batch_size,
                'page_size': self.config.page_size
            },
            'field_mapping': self.field_mapper.get_mapping_summary(),
            'database': {
                'host': self.config.database.host,
                'port': self.config.database.port,
                'database': self.config.database.database
            },
            'yida': {
                'app_type': self.config.yida.app_type,
                'form_uuid': self.config.yida.form_uuid
            }
        }
        
        # 测试连接并获取状态
        try:
            connection_results = self.test_connections()
            info['connections'] = connection_results
        except Exception as e:
            info['connections'] = {'error': str(e)}
        
        return info
    
    def validate_data(self) -> Dict[str, Any]:
        """
        验证数据
        
        Returns:
            Dict[str, Any]: 验证结果
        """
        results = {
            'mysql_sample': [],
            'yida_sample': [],
            'validation_errors': []
        }
        
        try:
            # 获取MySQL样本数据
            mysql_sample = self.mysql_client.get_sample_data(3)
            results['mysql_sample'] = mysql_sample
            
            # 获取宜搭样本数据
            yida_sample = self.yida_client.get_form_data(page_size=3)
            results['yida_sample'] = yida_sample
            
            # 验证数据格式
            for item in mysql_sample:
                errors = self.data_processor.validate_data(item)
                if errors:
                    results['validation_errors'].extend(errors)
            
        except Exception as e:
            results['validation_errors'].append(f"验证过程出错: {str(e)}")
        
        return results 