# -*- coding: utf-8 -*-
"""
MySQL客户端

封装MySQL数据库操作，包括连接管理、查询执行等。
"""

import pymysql
import logging
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, timedelta


class MySQLClient:
    """MySQL客户端"""
    
    def __init__(self, config):
        """
        初始化MySQL客户端
        
        Args:
            config: 同步配置对象
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        self.connection = None
        
    def connect(self) -> bool:
        """
        连接MySQL数据库
        
        Returns:
            bool: 连接是否成功
        """
        try:
            # 构建连接参数
            db_config = self.config.database.to_dict()
            
            # 处理cursorclass
            if db_config.get('cursorclass') == 'DictCursor':
                db_config['cursorclass'] = pymysql.cursors.DictCursor
            else:
                db_config['cursorclass'] = pymysql.cursors.Cursor
            
            self.connection = pymysql.connect(**db_config)
            self.logger.info(f"MySQL连接成功: {self.config.database.host}:{self.config.database.port}/{self.config.database.database}")
            return True
            
        except Exception as e:
            self.logger.error(f"MySQL连接失败: {str(e)}")
            return False
    
    def disconnect(self):
        """断开MySQL连接"""
        if self.connection:
            try:
                self.connection.close()
                self.logger.info("MySQL连接已断开")
            except Exception as e:
                self.logger.error(f"断开MySQL连接时出错: {str(e)}")
            finally:
                self.connection = None
    
    def execute_query(self, sql: str, params: Optional[Tuple] = None) -> List[Dict[str, Any]]:
        """
        执行查询语句
        
        Args:
            sql: SQL查询语句
            params: 查询参数
            
        Returns:
            List[Dict[str, Any]]: 查询结果列表
        """
        if not self.connection:
            if not self.connect():
                raise Exception("无法连接到MySQL数据库")
        
        try:
            cursor = self.connection.cursor()
            
            # 记录查询信息
            self.logger.info(f"执行SQL查询: {sql}")
            if params:
                self.logger.info(f"查询参数: {params}")
            
            # 执行查询
            if params:
                cursor.execute(sql, params)
            else:
                cursor.execute(sql)
            
            # 获取结果
            result = cursor.fetchall()
            
            # 如果是DictCursor，结果已经是字典格式
            if isinstance(result, list) and result and isinstance(result[0], dict):
                return result
            else:
                # 如果不是DictCursor，需要手动转换
                columns = [desc[0] for desc in cursor.description]
                return [dict(zip(columns, row)) for row in result]
                
        except Exception as e:
            self.logger.error(f"执行SQL查询失败: {str(e)}")
            self.logger.error(f"SQL: {sql}")
            if params:
                self.logger.error(f"参数: {params}")
            raise
    
    def get_data(self, start_date: Optional[str] = None, end_date: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        获取MySQL数据
        
        Args:
            start_date: 开始日期，格式：YYYY-MM-DD
            end_date: 结束日期，格式：YYYY-MM-DD
            
        Returns:
            List[Dict[str, Any]]: 数据列表
        """
        try:
            sql = self.config.sql_query
            params = None
            
            # 处理日期参数
            if start_date is not None or end_date is not None:
                # 设置默认日期
                if end_date is None:
                    end_date = datetime.now().strftime('%Y-%m-%d')
                if start_date is None:
                    start_date = end_date
                
                # 检查SQL中是否包含日期占位符
                if '%s' in sql:
                    # 如果SQL中有占位符，添加日期条件
                    if 'WHERE' in sql.upper():
                        sql += f" AND DATE_FORMAT({self.config.date_field}, '%%Y-%%m-%%d') BETWEEN %s AND %s"
                    else:
                        sql += f" WHERE DATE_FORMAT({self.config.date_field}, '%%Y-%%m-%%d') BETWEEN %s AND %s"
                    params = (start_date, end_date)
                else:
                    # 如果SQL中没有占位符，直接替换日期
                    sql = sql.replace('{start_date}', start_date)
                    sql = sql.replace('{end_date}', end_date)
            else:
                # 默认查询当天的增量数据
                if self.config.sync_strategy == 'incremental':
                    current_date = datetime.now().strftime('%Y-%m-%d')
                    if '%s' in sql:
                        if 'WHERE' in sql.upper():
                            sql += f" AND DATE_FORMAT(updated_time, '%%Y-%%m-%%d') = %s"
                        else:
                            sql += f" WHERE DATE_FORMAT(updated_time, '%%Y-%%m-%%d') = %s"
                        params = (current_date,)
                    else:
                        sql = sql.replace('{current_date}', current_date)
            
            # 执行查询
            result = self.execute_query(sql, params)
            
            # 记录查询信息
            if start_date is not None or end_date is not None:
                self.logger.info(f"MySQL查询成功，时间段: {start_date} 至 {end_date}，共获取 {len(result)} 条记录")
            else:
                self.logger.info(f"MySQL查询成功，增量数据，共获取 {len(result)} 条记录")
            
            return result
            
        except Exception as e:
            self.logger.error(f"获取MySQL数据失败: {str(e)}")
            return []
    
    def test_connection(self) -> bool:
        """
        测试MySQL连接
        
        Returns:
            bool: 连接是否成功
        """
        try:
            if not self.connection:
                return self.connect()
            
            # 执行简单查询测试连接
            cursor = self.connection.cursor()
            cursor.execute("SELECT 1")
            cursor.fetchone()
            self.logger.info("MySQL连接测试成功")
            return True
            
        except Exception as e:
            self.logger.error(f"MySQL连接测试失败: {str(e)}")
            return False
    
    def get_table_info(self, table_name: str) -> Dict[str, Any]:
        """
        获取表信息
        
        Args:
            table_name: 表名
            
        Returns:
            Dict[str, Any]: 表信息
        """
        try:
            sql = f"""
                SELECT 
                    COLUMN_NAME,
                    DATA_TYPE,
                    IS_NULLABLE,
                    COLUMN_DEFAULT,
                    COLUMN_COMMENT
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_SCHEMA = %s AND TABLE_NAME = %s
                ORDER BY ORDINAL_POSITION
            """
            
            result = self.execute_query(sql, (self.config.database.database, table_name))
            
            return {
                "table_name": table_name,
                "database": self.config.database.database,
                "columns": result
            }
            
        except Exception as e:
            self.logger.error(f"获取表信息失败: {str(e)}")
            return {"error": str(e)}
    
    def get_table_count(self, table_name: str) -> int:
        """
        获取表记录数
        
        Args:
            table_name: 表名
            
        Returns:
            int: 记录数
        """
        try:
            sql = f"SELECT COUNT(*) as count FROM {table_name}"
            result = self.execute_query(sql)
            
            if result and len(result) > 0:
                return result[0].get('count', 0)
            return 0
            
        except Exception as e:
            self.logger.error(f"获取表记录数失败: {str(e)}")
            return 0
    
    def validate_sql(self, sql: str) -> List[str]:
        """
        验证SQL语句
        
        Args:
            sql: SQL语句
            
        Returns:
            List[str]: 错误信息列表
        """
        errors = []
        
        # 检查基本语法
        if not sql.strip():
            errors.append("SQL语句不能为空")
            return errors
        
        # 检查是否包含SELECT
        if not sql.strip().upper().startswith('SELECT'):
            errors.append("SQL语句必须以SELECT开头")
        
        # 检查是否包含必要的字段
        required_fields = self.config.primary_key_fields + self.config.compare_fields
        for field in required_fields:
            if field not in sql:
                errors.append(f"SQL语句中缺少必要字段: {field}")
        
        # 检查日期字段
        if self.config.date_field and self.config.date_field not in sql:
            errors.append(f"SQL语句中缺少日期字段: {self.config.date_field}")
        
        return errors
    
    def get_sample_data(self, limit: int = 5) -> List[Dict[str, Any]]:
        """
        获取样本数据
        
        Args:
            limit: 限制条数
            
        Returns:
            List[Dict[str, Any]]: 样本数据
        """
        try:
            # 在原始SQL基础上添加LIMIT
            sql = self.config.sql_query
            if 'LIMIT' not in sql.upper():
                sql += f" LIMIT {limit}"
            
            result = self.execute_query(sql)
            self.logger.info(f"获取样本数据成功，共 {len(result)} 条记录")
            return result
            
        except Exception as e:
            self.logger.error(f"获取样本数据失败: {str(e)}")
            return []
    
    def __enter__(self):
        """上下文管理器入口"""
        self.connect()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.disconnect() 