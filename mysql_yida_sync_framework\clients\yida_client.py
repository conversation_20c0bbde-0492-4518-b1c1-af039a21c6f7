# -*- coding: utf-8 -*-
"""
宜搭客户端

封装宜搭API操作，包括获取表单数据、更新数据、批量插入等。
"""

import json
import time
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime

# 设置日志级别为 WARNING，这样就不会显示 DEBUG 信息
logging.getLogger("alibabacloud_credentials").setLevel(logging.WARNING)

from alibabacloud_dingtalk.yida_2_0.client import Client as DingTalkYidaClient
from alibabacloud_tea_openapi import models as open_api_models
from alibabacloud_dingtalk.yida_2_0 import models as yida_models
from alibabacloud_tea_util import models as util_models
from alibabacloud_tea_util.client import Client as UtilClient
from alibabacloud_dingtalk.yida_1_0 import models as dingtalkyida__1__0_models
from alibabacloud_dingtalk.yida_1_0.client import Client as dingtalkyida_1_0Client


class YidaClient:
    """宜搭客户端"""
    
    def __init__(self, config, token_provider=None):
        """
        初始化宜搭客户端
        
        Args:
            config: 同步配置对象
            token_provider: Token提供者，如果为None则使用默认的get_token模块
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # 获取访问令牌
        if token_provider:
            self.access_token = token_provider.get_token()
        else:
            # 使用默认的token模块
            try:
                from get_token import token
                self.access_token = token.get_token()
            except ImportError:
                raise ImportError("无法导入get_token模块，请提供token_provider或确保get_token.py文件存在")
        
        # 创建客户端
        self.client = self._create_client()
        
    def _create_client(self) -> dingtalkyida_1_0Client:
        """
        创建宜搭客户端
        
        Returns:
            dingtalkyida_1_0Client: 宜搭客户端实例
        """
        config = open_api_models.Config(
            protocol='https',
            region_id='central'
        )
        return dingtalkyida_1_0Client(config)
    
    def get_form_data(self, page_size: int = None, search_condition: Optional[List[Dict]] = None) -> List[Dict[str, Any]]:
        """
        获取宜搭表单数据
        
        Args:
            page_size: 每页数据条数，默认使用配置中的page_size
            search_condition: 查询条件，可选
            
        Returns:
            List[Dict[str, Any]]: 表单数据列表
        """
        if page_size is None:
            page_size = self.config.page_size
            
        try:
            all_data = []
            current_page = 1
            
            while True:
                headers = dingtalkyida__1__0_models.SearchFormDataSecondGenerationNoTableFieldHeaders()
                headers.x_acs_dingtalk_access_token = self.access_token
                
                request = dingtalkyida__1__0_models.SearchFormDataSecondGenerationNoTableFieldRequest(
                    page_number=current_page,
                    form_uuid=self.config.yida.form_uuid,
                    search_condition=json.dumps(search_condition) if search_condition else None,
                    system_token=self.config.yida.system_token,
                    page_size=page_size,
                    user_id=self.config.yida.user_id,
                    app_type=self.config.yida.app_type
                )
                
                # 记录请求参数
                self.logger.debug(f"Request Parameters - Page {current_page}:")
                self.logger.debug(f"Headers: {headers}")
                self.logger.debug(f"Request: {request}")
                
                result = self.client.search_form_data_second_generation_no_table_field_with_options(
                    request, 
                    headers, 
                    util_models.RuntimeOptions()
                )
                
                # 记录响应结果
                self.logger.debug(f"Response - Page {current_page}:")
                
                if not result or not result.body or not result.body.data:
                    break
                
                # 提取指定字段
                for item in result.body.data:
                    filtered_data = {
                        'formInstanceId': item.form_instance_id,
                        'formData': item.form_data
                    }
                    all_data.append(filtered_data)
                
                self.logger.info(f"第 {current_page} 页获取到 {len(result.body.data)} 条记录")
                
                # 添加延时避免请求过于频繁
                time.sleep(0.5)
                
                # 如果获取的数据少于页大小，说明已经是最后一页
                if len(result.body.data) < page_size:
                    break
                    
                current_page += 1
            
            self.logger.info(f"查询完成，共获取到 {len(all_data)} 条记录")
            return all_data
            
        except Exception as e:
            error_msg = f"获取表单数据失败: {str(e)}"
            if hasattr(e, 'code') and hasattr(e, 'message'):
                error_msg += f" (错误码: {e.code}, 错误信息: {e.message})"
            self.logger.error(error_msg)
            raise Exception(error_msg)
    
    def update_form_data(self, form_instance_id: str, new_data: Dict[str, Any]) -> bool:
        """
        更新宜搭表单数据
        
        Args:
            form_instance_id: 表单实例ID
            new_data: 新数据字典
            
        Returns:
            bool: 是否更新成功
        """
        try:
            client = DingTalkYidaClient(open_api_models.Config(
                protocol='https',
                region_id='central'
            ))
            
            headers = yida_models.UpdateFormDataHeaders()
            headers.x_acs_dingtalk_access_token = self.access_token
            
            # 处理日期字段
            processed_data = new_data.copy()
            if self.config.date_field:
                yida_date_field = self.config.field_mapping.get(self.config.date_field)
                if yida_date_field and yida_date_field in processed_data:
                    try:
                        date_value = processed_data[yida_date_field]
                        if isinstance(date_value, str):
                            # 如果是日期字符串，转换为时间戳
                            dt = datetime.strptime(date_value, self.config.date_format)
                            processed_data[yida_date_field] = int(dt.timestamp() * 1000)
                        self.logger.debug(f"更新数据日期字段转换: {date_value} -> {processed_data[yida_date_field]}")
                    except ValueError as e:
                        self.logger.error(f"更新数据日期格式转换错误: {date_value}, {str(e)}")
                        raise
            
            request = yida_models.UpdateFormDataRequest()
            request.app_type = self.config.yida.app_type
            request.system_token = self.config.yida.system_token
            request.user_id = self.config.yida.user_id
            request.language = self.config.yida.language
            request.form_instance_id = form_instance_id
            request.form_uuid = self.config.yida.form_uuid
            request.update_form_data_json = json.dumps(processed_data, ensure_ascii=False)
            request.use_alias = False
            request.use_latest_version = False
            
            response = client.update_form_data_with_options(request, headers, util_models.RuntimeOptions())
            self.logger.info(f"更新表单数据成功: {form_instance_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"更新表单数据失败: {str(e)}")
            return False
    
    def batch_create_form_data(self, data_list: List[Dict[str, Any]], batch_size: int = None) -> bool:
        """
        批量插入宜搭表单数据
        
        Args:
            data_list: 数据列表
            batch_size: 批次大小，默认使用配置中的batch_size
            
        Returns:
            bool: 是否插入成功
        """
        if batch_size is None:
            batch_size = self.config.batch_size
            
        try:
            client = dingtalkyida_1_0Client(open_api_models.Config(
                protocol='https',
                region_id='central'
            ))
            
            headers = dingtalkyida__1__0_models.BatchSaveFormDataHeaders()
            headers.x_acs_dingtalk_access_token = self.access_token
            
            # 将数据列表分成多个批次
            for i in range(0, len(data_list), batch_size):
                batch_data = data_list[i:i + batch_size]
                processed_data_list = []
                
                for item in batch_data:
                    processed_item = {}
                    for key, value in item.items():
                        # 处理日期字段
                        if key == self.config.field_mapping.get(self.config.date_field):
                            try:
                                if isinstance(value, str):
                                    dt = datetime.strptime(value, self.config.date_format)
                                    processed_item[key] = int(dt.timestamp() * 1000)
                                else:
                                    processed_item[key] = value
                                self.logger.debug(f"批量插入日期字段转换: {value} -> {processed_item[key]}")
                            except ValueError as e:
                                self.logger.error(f"批量插入日期格式转换错误: {value}, {str(e)}")
                                continue
                        # 处理数值字段
                        elif key.startswith('numberField_'):
                            try:
                                if value is None or value == '':
                                    value = 0
                                processed_item[f"{key}_value"] = str(value)  # 添加字符串版本
                                processed_item[key] = value  # 保持原始值
                            except ValueError as e:
                                self.logger.error(f"数值转换错误: {value}, {str(e)}")
                                continue
                        else:
                            processed_item[key] = value
                    processed_data_list.append(processed_item)
                
                form_data_json_list = [json.dumps(item, ensure_ascii=False) for item in processed_data_list]
                
                request = dingtalkyida__1__0_models.BatchSaveFormDataRequest(
                    no_execute_expression=True,
                    form_uuid=self.config.yida.form_uuid,
                    app_type=self.config.yida.app_type,
                    asynchronous_execution=True,
                    system_token=self.config.yida.system_token,
                    keep_running_after_exception=True,
                    user_id=self.config.yida.user_id,
                    form_data_json_list=form_data_json_list
                )
                
                try:
                    response = client.batch_save_form_data_with_options(request, headers, util_models.RuntimeOptions())
                    
                    # 记录详细的响应信息
                    self.logger.info(f"批量插入响应状态码: {response.status_code}")
                    self.logger.debug(f"批量插入响应头: {response.headers}")
                    self.logger.debug(f"批量插入响应体: {response.body}")
                    
                    # 检查响应是否成功
                    if response.status_code == 200:
                        if hasattr(response.body, 'result') and response.body.result:
                            self.logger.info(f"批量插入表单数据成功，批次 {i//batch_size + 1}，共 {len(batch_data)} 条记录")
                            self.logger.debug(f"成功插入的数据ID: {response.body.result}")
                        else:
                            self.logger.warning(f"批量插入响应成功但未返回结果，批次 {i//batch_size + 1}")
                    else:
                        self.logger.error(f"批量插入表单数据失败，批次 {i//batch_size + 1}: {response.status_code}")
                        self.logger.error(f"错误响应: {response.body}")
                        return False
                        
                except Exception as e:
                    self.logger.error(f"批量插入表单数据失败，批次 {i//batch_size + 1}: {str(e)}")
                    return False
                
                # 添加延时避免请求过于频繁
                time.sleep(5)
            
            return True
            
        except Exception as e:
            self.logger.error(f"批量插入表单数据失败: {str(e)}")
            return False
    
    def test_connection(self) -> bool:
        """
        测试宜搭连接
        
        Returns:
            bool: 连接是否成功
        """
        try:
            # 尝试获取第一页数据来测试连接
            test_data = self.get_form_data(page_size=1)
            self.logger.info("宜搭连接测试成功")
            return True
        except Exception as e:
            self.logger.error(f"宜搭连接测试失败: {str(e)}")
            return False
    
    def get_form_info(self) -> Dict[str, Any]:
        """
        获取表单信息
        
        Returns:
            Dict[str, Any]: 表单信息
        """
        try:
            # 获取少量数据来分析表单结构
            sample_data = self.get_form_data(page_size=5)
            
            if not sample_data:
                return {"error": "无法获取表单数据"}
            
            # 分析第一个数据项的结构
            first_item = sample_data[0]
            form_data = first_item.get('formData', {})
            
            return {
                "form_uuid": self.config.yida.form_uuid,
                "app_type": self.config.yida.app_type,
                "fields": list(form_data.keys()),
                "sample_data": form_data,
                "total_records": len(sample_data) if len(sample_data) < 5 else ">5"
            }
            
        except Exception as e:
            self.logger.error(f"获取表单信息失败: {str(e)}")
            return {"error": str(e)} 