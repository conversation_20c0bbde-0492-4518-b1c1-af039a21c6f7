# -*- coding: utf-8 -*-
"""
数据验证工具类

提供各种数据验证功能。
"""

import re
from typing import Dict, List, Any, Optional, Union
from datetime import datetime


class Validators:
    """数据验证器"""
    
    @staticmethod
    def validate_email(email: str) -> bool:
        """
        验证邮箱格式
        
        Args:
            email: 邮箱地址
            
        Returns:
            bool: 是否有效
        """
        if not email:
            return False
        
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return bool(re.match(pattern, email))
    
    @staticmethod
    def validate_phone(phone: str) -> bool:
        """
        验证手机号格式
        
        Args:
            phone: 手机号
            
        Returns:
            bool: 是否有效
        """
        if not phone:
            return False
        
        # 中国手机号格式
        pattern = r'^1[3-9]\d{9}$'
        return bool(re.match(pattern, phone))
    
    @staticmethod
    def validate_date(date_str: str, format_str: str = '%Y-%m-%d') -> bool:
        """
        验证日期格式
        
        Args:
            date_str: 日期字符串
            format_str: 日期格式
            
        Returns:
            bool: 是否有效
        """
        if not date_str:
            return False
        
        try:
            datetime.strptime(date_str, format_str)
            return True
        except ValueError:
            return False
    
    @staticmethod
    def validate_number(value: Any) -> bool:
        """
        验证数值
        
        Args:
            value: 数值
            
        Returns:
            bool: 是否有效
        """
        if value is None:
            return False
        
        try:
            float(value)
            return True
        except (ValueError, TypeError):
            return False
    
    @staticmethod
    def validate_integer(value: Any) -> bool:
        """
        验证整数
        
        Args:
            value: 数值
            
        Returns:
            bool: 是否有效
        """
        if value is None:
            return False
        
        try:
            int(value)
            return True
        except (ValueError, TypeError):
            return False
    
    @staticmethod
    def validate_required(value: Any) -> bool:
        """
        验证必填字段
        
        Args:
            value: 字段值
            
        Returns:
            bool: 是否有效
        """
        if value is None:
            return False
        
        if isinstance(value, str):
            return bool(value.strip())
        
        return True
    
    @staticmethod
    def validate_length(value: str, min_length: int = 0, max_length: int = None) -> bool:
        """
        验证字符串长度
        
        Args:
            value: 字符串值
            min_length: 最小长度
            max_length: 最大长度
            
        Returns:
            bool: 是否有效
        """
        if not isinstance(value, str):
            return False
        
        length = len(value)
        
        if length < min_length:
            return False
        
        if max_length is not None and length > max_length:
            return False
        
        return True
    
    @staticmethod
    def validate_range(value: Union[int, float], min_value: Union[int, float] = None, 
                      max_value: Union[int, float] = None) -> bool:
        """
        验证数值范围
        
        Args:
            value: 数值
            min_value: 最小值
            max_value: 最大值
            
        Returns:
            bool: 是否有效
        """
        if not Validators.validate_number(value):
            return False
        
        num_value = float(value)
        
        if min_value is not None and num_value < min_value:
            return False
        
        if max_value is not None and num_value > max_value:
            return False
        
        return True
    
    @staticmethod
    def validate_enum(value: Any, allowed_values: List[Any]) -> bool:
        """
        验证枚举值
        
        Args:
            value: 值
            allowed_values: 允许的值列表
            
        Returns:
            bool: 是否有效
        """
        return value in allowed_values
    
    @staticmethod
    def validate_url(url: str) -> bool:
        """
        验证URL格式
        
        Args:
            url: URL字符串
            
        Returns:
            bool: 是否有效
        """
        if not url:
            return False
        
        pattern = r'^https?://(?:[-\w.])+(?:[:\d]+)?(?:/(?:[\w/_.])*(?:\?(?:[\w&=%.])*)?(?:#(?:[\w.])*)?)?$'
        return bool(re.match(pattern, url))
    
    @staticmethod
    def validate_mysql_data(data: Dict[str, Any], required_fields: List[str], 
                           field_rules: Dict[str, Dict[str, Any]] = None) -> List[str]:
        """
        验证MySQL数据
        
        Args:
            data: 数据字典
            required_fields: 必填字段列表
            field_rules: 字段验证规则
            
        Returns:
            List[str]: 错误信息列表
        """
        errors = []
        
        # 验证必填字段
        for field in required_fields:
            if not Validators.validate_required(data.get(field)):
                errors.append(f"必填字段 '{field}' 为空")
        
        # 验证字段规则
        if field_rules:
            for field, rules in field_rules.items():
                if field in data:
                    value = data[field]
                    
                    # 验证字段类型
                    if 'type' in rules:
                        field_type = rules['type']
                        if field_type == 'number' and not Validators.validate_number(value):
                            errors.append(f"字段 '{field}' 不是有效的数值")
                        elif field_type == 'integer' and not Validators.validate_integer(value):
                            errors.append(f"字段 '{field}' 不是有效的整数")
                        elif field_type == 'date' and not Validators.validate_date(value):
                            errors.append(f"字段 '{field}' 不是有效的日期格式")
                        elif field_type == 'email' and not Validators.validate_email(value):
                            errors.append(f"字段 '{field}' 不是有效的邮箱格式")
                        elif field_type == 'phone' and not Validators.validate_phone(value):
                            errors.append(f"字段 '{field}' 不是有效的手机号格式")
                        elif field_type == 'url' and not Validators.validate_url(value):
                            errors.append(f"字段 '{field}' 不是有效的URL格式")
                    
                    # 验证长度
                    if 'min_length' in rules or 'max_length' in rules:
                        min_length = rules.get('min_length', 0)
                        max_length = rules.get('max_length')
                        if not Validators.validate_length(str(value), min_length, max_length):
                            errors.append(f"字段 '{field}' 长度不符合要求")
                    
                    # 验证范围
                    if 'min_value' in rules or 'max_value' in rules:
                        min_value = rules.get('min_value')
                        max_value = rules.get('max_value')
                        if not Validators.validate_range(value, min_value, max_value):
                            errors.append(f"字段 '{field}' 值超出范围")
                    
                    # 验证枚举
                    if 'allowed_values' in rules:
                        if not Validators.validate_enum(value, rules['allowed_values']):
                            errors.append(f"字段 '{field}' 值不在允许范围内")
        
        return errors
    
    @staticmethod
    def validate_yida_data(data: Dict[str, Any], field_mapping: Dict[str, str]) -> List[str]:
        """
        验证宜搭数据
        
        Args:
            data: 宜搭数据字典
            field_mapping: 字段映射
            
        Returns:
            List[str]: 错误信息列表
        """
        errors = []
        
        # 检查必要字段是否存在
        for yida_field in field_mapping.values():
            if yida_field not in data:
                errors.append(f"宜搭字段 '{yida_field}' 不存在")
        
        # 验证字段值
        for field_name, value in data.items():
            if field_name.startswith('numberField_'):
                if not Validators.validate_number(value):
                    errors.append(f"数值字段 '{field_name}' 格式错误: {value}")
            elif field_name.startswith('dateField_'):
                if value and not Validators.validate_number(value):
                    errors.append(f"日期字段 '{field_name}' 格式错误: {value}")
            elif field_name.startswith('textField_'):
                if value and not isinstance(value, str):
                    errors.append(f"文本字段 '{field_name}' 格式错误: {value}")
        
        return errors
    
    @staticmethod
    def sanitize_string(value: str) -> str:
        """
        清理字符串
        
        Args:
            value: 原始字符串
            
        Returns:
            str: 清理后的字符串
        """
        if not isinstance(value, str):
            return str(value) if value is not None else ''
        
        # 移除首尾空白字符
        cleaned = value.strip()
        
        # 移除控制字符
        cleaned = ''.join(char for char in cleaned if ord(char) >= 32 or char in '\n\r\t')
        
        return cleaned
    
    @staticmethod
    def sanitize_number(value: Any) -> Union[int, float, None]:
        """
        清理数值
        
        Args:
            value: 原始值
            
        Returns:
            Union[int, float, None]: 清理后的数值
        """
        if value is None:
            return None
        
        try:
            if isinstance(value, str):
                # 移除货币符号和千分位分隔符
                cleaned = value.replace('¥', '').replace(',', '').replace(' ', '')
                if '.' in cleaned:
                    return float(cleaned)
                else:
                    return int(cleaned)
            else:
                return float(value)
        except (ValueError, TypeError):
            return None
    
    @staticmethod
    def validate_config(config: Dict[str, Any]) -> List[str]:
        """
        验证配置
        
        Args:
            config: 配置字典
            
        Returns:
            List[str]: 错误信息列表
        """
        errors = []
        
        # 验证数据库配置
        if 'database' not in config:
            errors.append("缺少数据库配置")
        else:
            db_config = config['database']
            required_db_fields = ['host', 'user', 'database']
            for field in required_db_fields:
                if field not in db_config or not db_config[field]:
                    errors.append(f"数据库配置缺少必要字段: {field}")
        
        # 验证宜搭配置
        if 'yida' not in config:
            errors.append("缺少宜搭配置")
        else:
            yida_config = config['yida']
            required_yida_fields = ['app_type', 'system_token', 'user_id', 'form_uuid']
            for field in required_yida_fields:
                if field not in yida_config or not yida_config[field]:
                    errors.append(f"宜搭配置缺少必要字段: {field}")
        
        # 验证字段映射
        if 'field_mapping' not in config or not config['field_mapping']:
            errors.append("缺少字段映射配置")
        
        # 验证主键字段
        if 'primary_key_fields' not in config or not config['primary_key_fields']:
            errors.append("缺少主键字段配置")
        
        # 验证SQL查询
        if 'sql_query' not in config or not config['sql_query']:
            errors.append("缺少SQL查询配置")
        
        return errors 