# -*- coding: utf-8 -*-
"""
销售数据同步示例

展示如何使用MySQL到宜搭同步框架进行销售数据同步。
"""

import sys
import os
import argparse
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from mysql_yida_sync_framework import SyncConfig, SyncClient


def create_sales_sync_config() -> SyncConfig:
    """
    创建销售数据同步配置
    
    Returns:
        SyncConfig: 同步配置对象
    """
    # 数据库配置
    database_config = {
        'host': '**************',
        'port': 3306,
        'user': 'c_hxp_ro_prod',
        'password': 'xm9P06O7ezGi6PZt',
        'database': 'yx_business',
        'charset': 'utf8mb4',
        'cursorclass': 'DictCursor'
    }
    
    # 宜搭配置
    yida_config = {
        'app_type': 'APP_D7E6ZB94ZUL5Q1GUAOLD',
        'system_token': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2',
        'user_id': 'hexuepeng',
        'language': 'zh_CN',
        'form_uuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30'
    }
    
    # 字段映射
    field_mapping = {
        'project_code': 'textField_m9nw1k6w',
        'project_name': 'textField_m9nw1k6x', 
        'store_code': 'textField_m9nw1k6y',
        'store_name': 'textField_m9nw1k6z',
        'sale_date': 'dateField_m9nw1k71',
        'online_amount': 'numberField_m9nw1k73',
        'offline_amount': 'numberField_m9nw1k75', 
        'total_amount': 'numberField_m9nw1k77',
        'order_count': 'numberField_m9nw1k79',
        'report_source': 'textField_mbnv3d7h',
        'url': 'textField_maq9pxw4'
    }
    
    # 需要比较的字段
    compare_fields = [
        'online_amount',    # 线上销售额
        'offline_amount',   # 线下销售额
        'total_amount',     # 总销售额
        'order_count',      # 订单数量
        'report_source',    # 上报来源
        'url',              # 附件
        'store_name'        # 店铺名称
    ]
    
    # 主键字段
    primary_key_fields = ['project_code', 'store_code', 'sale_date']
    
    # SQL查询
    sql_query = """
        SELECT 
            a.project_code,
            b.name AS project_name,
            a.store_code,
            c.name AS store_name,
            DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sale_date,
            a.online_amount AS online_amount,
            a.offline_amount AS offline_amount,
            a.total_amount AS total_amount,
            a.order_count AS order_count,
            case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
            d.url
        FROM
            yx_b_sales_record a
                JOIN
            yx_b_projects b ON a.project_code = b.code
                JOIN
            yx_b_tenants c ON a.store_code = c.code
                LEFT JOIN
            yx_b_annex d ON a.code = d.association_id
        WHERE
            a.status = 2 AND a.deleted = 0
                AND c.deleted = 0
                AND d.deleted = 0
    """
    
    # 创建配置对象
    config = SyncConfig(
        name="sales_sync",
        description="销售数据同步配置",
        database=database_config,
        yida=yida_config,
        field_mapping=field_mapping,
        compare_fields=compare_fields,
        primary_key_fields=primary_key_fields,
        date_field='sale_date',
        date_format='%Y-%m-%d',
        sql_query=sql_query,
        sync_strategy='incremental',
        batch_size=50,
        page_size=50,
        log_level='INFO',
        max_retries=3,
        retry_delay=5
    )
    
    return config


def create_monthly_sales_sync_config() -> SyncConfig:
    """
    创建月度销售数据同步配置
    
    Returns:
        SyncConfig: 同步配置对象
    """
    # 数据库配置
    database_config = {
        'host': '**************',
        'port': 3306,
        'user': 'c_hxp_ro_prod',
        'password': 'xm9P06O7ezGi6PZt',
        'database': 'yx_business',
        'charset': 'utf8mb4',
        'cursorclass': 'DictCursor'
    }
    
    # 宜搭配置
    yida_config = {
        'app_type': 'APP_D7E6ZB94ZUL5Q1GUAOLD',
        'system_token': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2',
        'user_id': 'hexuepeng',
        'language': 'zh_CN',
        'form_uuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5'
    }
    
    # 字段映射
    field_mapping = {
        'project_code': 'textField_m9tojheo',
        'project_name': 'textField_m9tojhep', 
        'store_code': 'textField_m9tojheq',
        'store_name': 'textField_m9tojher',
        'sale_date': 'dateField_m9tojheu',
        'online_amount': 'numberField_m9tojhev',
        'offline_amount': 'numberField_m9tojhew', 
        'total_amount': 'numberField_m9tojhex',
        'order_count': 'numberField_m9tojhey'
    }
    
    # 需要比较的字段
    compare_fields = [
        'online_amount',    # 线上销售额
        'offline_amount',   # 线下销售额
        'total_amount',     # 总销售额
        'order_count'       # 订单数量
    ]
    
    # 主键字段
    primary_key_fields = ['project_code', 'store_code', 'sale_date']
    
    # SQL查询
    sql_query = """
        SELECT 
            a.project_code, b.name as project_name,
            a.store_code, c.name as store_name,
            DATE_FORMAT(a.sales_time, '%Y-%m') as sale_date,
            SUM(a.online_amount) as online_amount,
            SUM(a.offline_amount) as offline_amount,
            SUM(a.total_amount) as total_amount,
            SUM(order_count) as order_count
        FROM yx_b_sales_record a
        JOIN yx_b_projects b ON a.project_code = b.code
        JOIN yx_b_tenants c ON a.store_code = c.code
        WHERE a.status = 2 
            AND a.deleted = 0
            AND c.deleted = 0
            and sales_time>='2025-01-01'
        GROUP BY a.project_code, b.name, a.store_code, c.name, 
                 DATE_FORMAT(a.sales_time, '%Y-%m');
    """
    
    # 创建配置对象
    config = SyncConfig(
        name="monthly_sales_sync",
        description="月度销售数据同步配置",
        database=database_config,
        yida=yida_config,
        field_mapping=field_mapping,
        compare_fields=compare_fields,
        primary_key_fields=primary_key_fields,
        date_field='sale_date',
        date_format='%Y-%m',
        sql_query=sql_query,
        sync_strategy='full',
        batch_size=100,
        page_size=100,
        log_level='INFO',
        max_retries=3,
        retry_delay=5
    )
    
    return config


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='销售数据同步示例')
    parser.add_argument('--type', choices=['daily', 'monthly'], default='daily',
                       help='同步类型：daily(日数据) 或 monthly(月数据)')
    parser.add_argument('--start-date', help='开始日期，格式：YYYY-MM-DD')
    parser.add_argument('--end-date', help='结束日期，格式：YYYY-MM-DD')
    parser.add_argument('--test', action='store_true', help='仅测试连接，不执行同步')
    parser.add_argument('--validate', action='store_true', help='验证数据格式')
    
    args = parser.parse_args()
    
    try:
        # 根据类型选择配置
        if args.type == 'daily':
            config = create_sales_sync_config()
        else:
            config = create_monthly_sales_sync_config()
        
        print(f"使用配置: {config.name}")
        print(f"描述: {config.description}")
        
        # 创建同步客户端
        sync_client = SyncClient(config)
        
        # 测试连接
        print("\n=== 测试连接 ===")
        connection_results = sync_client.test_connections()
        for name, result in connection_results.items():
            status = "成功" if result else "失败"
            print(f"{name}连接: {status}")
        
        if not all(connection_results.values()):
            print("连接测试失败，请检查配置")
            return
        
        # 获取系统信息
        print("\n=== 系统信息 ===")
        system_info = sync_client.get_system_info()
        print(f"配置名称: {system_info['config']['name']}")
        print(f"同步策略: {system_info['config']['sync_strategy']}")
        print(f"字段映射数量: {system_info['field_mapping']['total_fields']}")
        print(f"比较字段数量: {system_info['field_mapping']['compare_fields']}")
        
        # 验证数据
        if args.validate:
            print("\n=== 验证数据 ===")
            validation_results = sync_client.validate_data()
            if validation_results['validation_errors']:
                print("数据验证错误:")
                for error in validation_results['validation_errors']:
                    print(f"  - {error}")
            else:
                print("数据验证通过")
            
            if validation_results['mysql_sample']:
                print(f"MySQL样本数据: {len(validation_results['mysql_sample'])} 条")
            if validation_results['yida_sample']:
                print(f"宜搭样本数据: {len(validation_results['yida_sample'])} 条")
        
        # 仅测试模式
        if args.test:
            print("\n测试模式完成")
            return
        
        # 执行同步
        print("\n=== 开始同步 ===")
        start_time = datetime.now()
        
        result = sync_client.sync_data(args.start_date, args.end_date)
        
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        print("\n=== 同步结果 ===")
        print(f"配置名称: {result['config_name']}")
        print(f"开始时间: {result['start_time']}")
        print(f"结束时间: {result['end_time']}")
        print(f"耗时: {duration:.2f} 秒")
        print(f"更新记录: {result['total_update']} 条")
        print(f"插入记录: {result['total_insert']} 条")
        print(f"错误记录: {result['total_error']} 条")
        print(f"同步状态: {'成功' if result['success'] else '失败'}")
        
    except Exception as e:
        print(f"执行失败: {str(e)}")
        return 1
    
    return 0


if __name__ == '__main__':
    exit(main()) 