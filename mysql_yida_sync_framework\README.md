# MySQL到宜搭同步框架

一个通用的MySQL数据库到宜搭表单数据同步框架，支持灵活的配置和自定义同步逻辑。

## 功能特性

- **配置驱动**: 通过配置文件或类属性配置所有必要参数
- **模块化设计**: 将宜搭客户端、数据同步逻辑、配置管理分离
- **类型安全**: 使用类型提示和数据验证
- **错误处理**: 完善的异常处理和日志记录
- **扩展性**: 支持不同的数据源和同步策略
- **批量操作**: 支持批量插入和更新操作
- **增量同步**: 支持增量同步和全量同步策略
- **数据验证**: 内置数据验证和清理功能

## 项目结构

```
mysql_yida_sync_framework/
├── __init__.py                 # 主包入口
├── core/                       # 核心模块
│   ├── __init__.py
│   ├── sync_config.py          # 配置管理类
│   ├── field_mapper.py         # 字段映射类
│   └── data_processor.py       # 数据处理类
├── clients/                    # 客户端模块
│   ├── __init__.py
│   ├── yida_client.py          # 宜搭客户端
│   └── mysql_client.py         # MySQL客户端
├── sync/                       # 同步模块
│   ├── __init__.py
│   └── sync_client.py          # 同步客户端
├── utils/                      # 工具模块
│   ├── __init__.py
│   ├── logger.py               # 日志工具
│   └── validators.py           # 数据验证工具
├── examples/                   # 示例模块
│   ├── __init__.py
│   ├── sales_sync_example.py   # 销售数据同步示例
│   └── config_example.py       # 配置示例
└── README.md                   # 使用说明文档
```

## 安装依赖

### 系统要求

- Python 3.7+
- MySQL 5.7+
- 宜搭应用访问权限

### 安装依赖包

```bash
pip install pymysql
pip install alibabacloud-dingtalk
pip install alibabacloud-tea-openapi
pip install alibabacloud-tea-util
```

### 获取宜搭访问令牌

确保你有 `get_token.py` 文件，包含获取宜搭访问令牌的逻辑：

```python
# get_token.py
from alibabacloud_dingtalk.oauth2_1_0.client import Client as dingtalkoauth2_1_0Client
from alibabacloud_tea_openapi import models as open_api_models
from alibabacloud_dingtalk.oauth2_1_0 import models as dingtalkoauth_2__1__0_models

class token:
    @staticmethod
    def get_token():
        # 实现获取访问令牌的逻辑
        pass
```

## 快速开始

### 1. 创建配置

```python
from mysql_yida_sync_framework import SyncConfig

# 数据库配置
database_config = {
    'host': 'your_mysql_host',
    'port': 3306,
    'user': 'your_username',
    'password': 'your_password',
    'database': 'your_database',
    'charset': 'utf8mb4',
    'cursorclass': 'DictCursor'
}

# 宜搭配置
yida_config = {
    'app_type': 'your_app_type',
    'system_token': 'your_system_token',
    'user_id': 'your_user_id',
    'language': 'zh_CN',
    'form_uuid': 'your_form_uuid'
}

# 字段映射
field_mapping = {
    'project_code': 'textField_xxx',
    'project_name': 'textField_xxx',
    'sale_date': 'dateField_xxx',
    'amount': 'numberField_xxx'
}

# 创建配置对象
config = SyncConfig(
    name="my_sync",
    description="我的数据同步配置",
    database=database_config,
    yida=yida_config,
    field_mapping=field_mapping,
    compare_fields=['amount', 'project_name'],
    primary_key_fields=['project_code', 'sale_date'],
    date_field='sale_date',
    date_format='%Y-%m-%d',
    sql_query="SELECT * FROM your_table WHERE status = 1",
    sync_strategy='incremental',
    batch_size=50,
    page_size=50
)
```

### 2. 执行同步

```python
from mysql_yida_sync_framework import SyncClient

# 创建同步客户端
sync_client = SyncClient(config)

# 测试连接
connection_results = sync_client.test_connections()
print(f"连接测试结果: {connection_results}")

# 执行同步
result = sync_client.sync_data()
print(f"同步结果: {result}")
```

### 3. 使用配置文件

```python
# 保存配置到文件
config.save_to_json_file("configs/my_config.json")

# 从文件加载配置
config = SyncConfig.from_json_file("configs/my_config.json")
```

## 配置说明

### 数据库配置

| 字段 | 类型 | 必填 | 说明 |
|------|------|------|------|
| host | str | 是 | MySQL主机地址 |
| port | int | 否 | MySQL端口，默认3306 |
| user | str | 是 | 数据库用户名 |
| password | str | 是 | 数据库密码 |
| database | str | 是 | 数据库名称 |
| charset | str | 否 | 字符集，默认utf8mb4 |
| cursorclass | str | 否 | 游标类型，默认DictCursor |

### 宜搭配置

| 字段 | 类型 | 必填 | 说明 |
|------|------|------|------|
| app_type | str | 是 | 宜搭应用类型 |
| system_token | str | 是 | 系统令牌 |
| user_id | str | 是 | 用户ID |
| language | str | 否 | 语言，默认zh_CN |
| form_uuid | str | 是 | 表单UUID |

### 字段映射

字段映射定义了MySQL字段到宜搭字段的对应关系：

```python
field_mapping = {
    'mysql_field_name': 'yida_field_name',
    'project_code': 'textField_xxx',
    'amount': 'numberField_xxx',
    'date': 'dateField_xxx'
}
```

### 同步策略

- **incremental**: 增量同步，只同步新增或修改的数据
- **full**: 全量同步，同步所有数据

### 其他配置

| 字段 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| batch_size | int | 50 | 批量操作大小 |
| page_size | int | 50 | 分页大小 |
| log_level | str | INFO | 日志级别 |
| max_retries | int | 3 | 最大重试次数 |
| retry_delay | int | 5 | 重试延迟（秒） |

## 使用示例

### 销售数据同步

```python
from mysql_yida_sync_framework.examples.sales_sync_example import create_sales_sync_config
from mysql_yida_sync_framework import SyncClient

# 创建销售数据同步配置
config = create_sales_sync_config()

# 创建同步客户端
sync_client = SyncClient(config)

# 执行同步
result = sync_client.sync_data('2024-01-01', '2024-01-31')
print(f"同步完成: {result}")
```

### 命令行使用

```bash
# 测试连接
python examples/sales_sync_example.py --test

# 验证数据
python examples/sales_sync_example.py --validate

# 执行同步
python examples/sales_sync_example.py --start-date 2024-01-01 --end-date 2024-01-31

# 月度数据同步
python examples/sales_sync_example.py --type monthly
```

## 高级功能

### 自定义字段转换器

```python
from mysql_yida_sync_framework import FieldMapper

# 创建字段映射器
field_mapper = FieldMapper(field_mapping, compare_fields)

# 添加自定义转换器
def custom_converter(value):
    # 自定义转换逻辑
    return value.upper()

field_mapper.add_converter('project_name', custom_converter)
```

### 数据验证

```python
from mysql_yida_sync_framework.utils.validators import Validators

# 验证MySQL数据
errors = Validators.validate_mysql_data(data, required_fields, field_rules)

# 验证宜搭数据
errors = Validators.validate_yida_data(data, field_mapping)

# 验证配置
errors = Validators.validate_config(config_dict)
```

### 日志管理

```python
from mysql_yida_sync_framework.utils.logger import Logger

# 设置日志
logger = Logger.setup_logging("my_sync", "INFO")

# 清理旧日志
Logger.cleanup_old_logs(days=30)
```

## 错误处理

框架提供了完善的错误处理机制：

1. **配置验证**: 在初始化时验证配置的有效性
2. **连接测试**: 测试数据库和宜搭连接
3. **数据验证**: 验证数据格式和完整性
4. **异常捕获**: 捕获并记录所有异常
5. **重试机制**: 支持失败重试
6. **日志记录**: 详细的日志记录

## 性能优化

1. **批量操作**: 使用批量插入和更新减少API调用
2. **分页查询**: 分页获取数据避免内存溢出
3. **连接池**: 复用数据库连接
4. **延时控制**: 控制请求频率避免限流
5. **增量同步**: 只同步变化的数据

## 常见问题

### Q: 如何处理宜搭字段类型不匹配？

A: 框架会自动处理字段类型转换，你也可以添加自定义转换器：

```python
def convert_amount(value):
    if value is None:
        return 0
    return float(value)

field_mapper.add_converter('amount', convert_amount)
```

### Q: 如何设置增量同步？

A: 在配置中设置 `sync_strategy='incremental'`，并在SQL中添加时间条件：

```sql
SELECT * FROM table WHERE updated_time >= %s
```

### Q: 如何处理大量数据？

A: 调整 `batch_size` 和 `page_size` 参数，并考虑分批处理：

```python
# 分批处理
for start_date, end_date in date_ranges:
    result = sync_client.sync_data(start_date, end_date)
```

### Q: 如何调试同步问题？

A: 使用以下方法：

1. 设置日志级别为 DEBUG
2. 使用 `--test` 参数测试连接
3. 使用 `--validate` 参数验证数据
4. 查看日志文件获取详细信息

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

MIT License

## 联系方式

如有问题或建议，请提交 Issue 或联系开发团队。 