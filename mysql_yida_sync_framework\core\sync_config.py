# -*- coding: utf-8 -*-
"""
同步配置类

定义数据同步所需的所有配置项，包括数据库配置、宜搭配置、字段映射等。
"""

from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass, field
import json
import os


@dataclass
class DatabaseConfig:
    """数据库配置"""
    host: str
    port: int = 3306
    user: str = ""
    password: str = ""
    database: str = ""
    charset: str = "utf8mb4"
    cursorclass: str = "DictCursor"
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'host': self.host,
            'port': self.port,
            'user': self.user,
            'password': self.password,
            'database': self.database,
            'charset': self.charset,
            'cursorclass': self.cursorclass
        }


@dataclass
class YidaConfig:
    """宜搭配置"""
    app_type: str
    system_token: str
    user_id: str
    language: str = "zh_CN"
    form_uuid: str = ""
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'APP_TYPE': self.app_type,
            'SYSTEM_TOKEN': self.system_token,
            'USER_ID': self.user_id,
            'LANGUAGE': self.language,
            'FORM_UUID': self.form_uuid
        }


@dataclass
class SyncConfig:
    """同步配置主类"""
    
    # 基础配置
    name: str = "default_sync"
    description: str = "数据同步配置"
    
    # 数据库配置
    database: DatabaseConfig = field(default_factory=DatabaseConfig)
    
    # 宜搭配置
    yida: YidaConfig = field(default_factory=YidaConfig)
    
    # 字段映射配置
    field_mapping: Dict[str, str] = field(default_factory=dict)
    
    # 需要比较的字段列表
    compare_fields: List[str] = field(default_factory=list)
    
    # 主键字段（用于生成唯一标识）
    primary_key_fields: List[str] = field(default_factory=list)
    
    # 日期字段配置
    date_field: Optional[str] = None
    date_format: str = "%Y-%m-%d"
    
    # SQL查询配置
    sql_query: str = ""
    sql_params: Dict[str, Any] = field(default_factory=dict)
    
    # 同步策略配置
    sync_strategy: str = "incremental"  # incremental, full
    batch_size: int = 50
    page_size: int = 50
    
    # 日志配置
    log_level: str = "INFO"
    log_file: str = ""
    
    # 错误处理配置
    max_retries: int = 3
    retry_delay: int = 5
    
    def __post_init__(self):
        """初始化后处理"""
        # 设置默认日志文件
        if not self.log_file:
            self.log_file = f"logs/sync_{self.name}_log.txt"
    
    def validate(self) -> List[str]:
        """验证配置的有效性"""
        errors = []
        
        # 验证数据库配置
        if not self.database.host:
            errors.append("数据库主机地址不能为空")
        if not self.database.user:
            errors.append("数据库用户名不能为空")
        if not self.database.database:
            errors.append("数据库名称不能为空")
            
        # 验证宜搭配置
        if not self.yida.app_type:
            errors.append("宜搭应用类型不能为空")
        if not self.yida.system_token:
            errors.append("宜搭系统令牌不能为空")
        if not self.yida.user_id:
            errors.append("宜搭用户ID不能为空")
        if not self.yida.form_uuid:
            errors.append("宜搭表单UUID不能为空")
            
        # 验证字段映射
        if not self.field_mapping:
            errors.append("字段映射不能为空")
            
        # 验证SQL查询
        if not self.sql_query:
            errors.append("SQL查询语句不能为空")
            
        # 验证主键字段
        if not self.primary_key_fields:
            errors.append("主键字段不能为空")
            
        return errors
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'name': self.name,
            'description': self.description,
            'database': self.database.to_dict(),
            'yida': self.yida.to_dict(),
            'field_mapping': self.field_mapping,
            'compare_fields': self.compare_fields,
            'primary_key_fields': self.primary_key_fields,
            'date_field': self.date_field,
            'date_format': self.date_format,
            'sql_query': self.sql_query,
            'sql_params': self.sql_params,
            'sync_strategy': self.sync_strategy,
            'batch_size': self.batch_size,
            'page_size': self.page_size,
            'log_level': self.log_level,
            'log_file': self.log_file,
            'max_retries': self.max_retries,
            'retry_delay': self.retry_delay
        }
    
    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> 'SyncConfig':
        """从字典创建配置对象"""
        # 处理嵌套的配置对象
        database_config = DatabaseConfig(**config_dict.get('database', {}))
        yida_config = YidaConfig(**config_dict.get('yida', {}))
        
        # 创建主配置对象
        config = cls(
            name=config_dict.get('name', 'default_sync'),
            description=config_dict.get('description', '数据同步配置'),
            database=database_config,
            yida=yida_config,
            field_mapping=config_dict.get('field_mapping', {}),
            compare_fields=config_dict.get('compare_fields', []),
            primary_key_fields=config_dict.get('primary_key_fields', []),
            date_field=config_dict.get('date_field'),
            date_format=config_dict.get('date_format', '%Y-%m-%d'),
            sql_query=config_dict.get('sql_query', ''),
            sql_params=config_dict.get('sql_params', {}),
            sync_strategy=config_dict.get('sync_strategy', 'incremental'),
            batch_size=config_dict.get('batch_size', 50),
            page_size=config_dict.get('page_size', 50),
            log_level=config_dict.get('log_level', 'INFO'),
            log_file=config_dict.get('log_file', ''),
            max_retries=config_dict.get('max_retries', 3),
            retry_delay=config_dict.get('retry_delay', 5)
        )
        
        return config
    
    @classmethod
    def from_json_file(cls, file_path: str) -> 'SyncConfig':
        """从JSON文件加载配置"""
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"配置文件不存在: {file_path}")
            
        with open(file_path, 'r', encoding='utf-8') as f:
            config_dict = json.load(f)
            
        return cls.from_dict(config_dict)
    
    def save_to_json_file(self, file_path: str):
        """保存配置到JSON文件"""
        # 确保目录存在
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(self.to_dict(), f, ensure_ascii=False, indent=2)
    
    def get_unique_key(self, data: Dict[str, Any]) -> str:
        """根据主键字段生成唯一标识"""
        key_parts = []
        for field in self.primary_key_fields:
            if field in data:
                key_parts.append(str(data[field]))
            else:
                key_parts.append('')
        return '_'.join(key_parts)
    
    def get_date_value(self, data: Dict[str, Any]) -> Optional[str]:
        """获取日期字段值"""
        if not self.date_field or self.date_field not in data:
            return None
        return str(data[self.date_field]) 