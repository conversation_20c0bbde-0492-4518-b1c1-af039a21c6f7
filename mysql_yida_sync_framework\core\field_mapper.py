# -*- coding: utf-8 -*-
"""
字段映射类

处理MySQL字段到宜搭字段的映射、转换和验证。
"""

from typing import Dict, List, Any, Optional, Union, Callable
from datetime import datetime
import logging


class FieldMapper:
    """字段映射管理器"""
    
    def __init__(self, field_mapping: Dict[str, str], compare_fields: List[str]):
        """
        初始化字段映射器
        
        Args:
            field_mapping: 字段映射字典 {mysql_field: yida_field}
            compare_fields: 需要比较的字段列表
        """
        self.field_mapping = field_mapping
        self.compare_fields = compare_fields
        self.reverse_mapping = {v: k for k, v in field_mapping.items()}
        
        # 字段类型映射
        self.field_types = self._detect_field_types()
        
        # 自定义转换器
        self.converters = {}
        
    def _detect_field_types(self) -> Dict[str, str]:
        """检测字段类型"""
        field_types = {}
        
        for mysql_field, yida_field in self.field_mapping.items():
            if yida_field.startswith('textField_'):
                field_types[mysql_field] = 'text'
            elif yida_field.startswith('numberField_'):
                field_types[mysql_field] = 'number'
            elif yida_field.startswith('dateField_'):
                field_types[mysql_field] = 'date'
            elif yida_field.startswith('selectField_'):
                field_types[mysql_field] = 'select'
            else:
                field_types[mysql_field] = 'unknown'
                
        return field_types
    
    def get_yida_field(self, mysql_field: str) -> Optional[str]:
        """获取MySQL字段对应的宜搭字段名"""
        return self.field_mapping.get(mysql_field)
    
    def get_mysql_field(self, yida_field: str) -> Optional[str]:
        """获取宜搭字段对应的MySQL字段名"""
        return self.reverse_mapping.get(yida_field)
    
    def get_field_type(self, mysql_field: str) -> str:
        """获取字段类型"""
        return self.field_types.get(mysql_field, 'unknown')
    
    def add_converter(self, field: str, converter: Callable):
        """添加自定义转换器"""
        self.converters[field] = converter
    
    def convert_value(self, field: str, value: Any, target_type: str = 'yida') -> Any:
        """
        转换字段值
        
        Args:
            field: 字段名
            value: 字段值
            target_type: 目标类型 ('yida' 或 'mysql')
            
        Returns:
            转换后的值
        """
        if value is None:
            return None
            
        # 应用自定义转换器
        if field in self.converters:
            return self.converters[field](value)
        
        field_type = self.get_field_type(field)
        
        if target_type == 'yida':
            return self._convert_to_yida(field, value, field_type)
        else:
            return self._convert_to_mysql(field, value, field_type)
    
    def _convert_to_yida(self, field: str, value: Any, field_type: str) -> Any:
        """转换为宜搭格式"""
        if field_type == 'number':
            return self._convert_number(value)
        elif field_type == 'date':
            return self._convert_date(value)
        elif field_type == 'text':
            return self._convert_text(value)
        else:
            return value
    
    def _convert_to_mysql(self, field: str, value: Any, field_type: str) -> Any:
        """转换为MySQL格式"""
        if field_type == 'number':
            return self._convert_number(value)
        elif field_type == 'date':
            return self._convert_date_to_mysql(value)
        elif field_type == 'text':
            return self._convert_text(value)
        else:
            return value
    
    def _convert_number(self, value: Any) -> Union[int, float]:
        """转换数值类型"""
        if value is None or value == '':
            return 0
        
        try:
            if isinstance(value, (int, float)):
                return value
            elif isinstance(value, str):
                # 移除可能的货币符号和千分位分隔符
                cleaned = value.replace('¥', '').replace(',', '').replace(' ', '')
                if '.' in cleaned:
                    return float(cleaned)
                else:
                    return int(cleaned)
            else:
                return float(value)
        except (ValueError, TypeError):
            logging.warning(f"无法转换数值: {value}，使用默认值0")
            return 0
    
    def _convert_date(self, value: Any) -> int:
        """转换日期为时间戳（毫秒）"""
        if value is None or value == '':
            return 0
        
        try:
            if isinstance(value, (int, float)):
                # 如果已经是时间戳，直接返回
                if value > 1000000000000:  # 毫秒时间戳
                    return int(value)
                else:  # 秒时间戳
                    return int(value * 1000)
            elif isinstance(value, str):
                # 尝试解析日期字符串
                for fmt in ['%Y-%m-%d', '%Y-%m-%d %H:%M:%S', '%Y/%m/%d', '%Y%m%d']:
                    try:
                        dt = datetime.strptime(value, fmt)
                        return int(dt.timestamp() * 1000)
                    except ValueError:
                        continue
                raise ValueError(f"无法解析日期格式: {value}")
            elif isinstance(value, datetime):
                return int(value.timestamp() * 1000)
            else:
                raise ValueError(f"不支持的日期类型: {type(value)}")
        except Exception as e:
            logging.error(f"日期转换失败: {value}, 错误: {str(e)}")
            return 0
    
    def _convert_date_to_mysql(self, value: Any) -> str:
        """转换日期为MySQL格式字符串"""
        if value is None or value == '':
            return None
        
        try:
            if isinstance(value, (int, float)):
                # 时间戳转换
                if value > 1000000000000:  # 毫秒时间戳
                    dt = datetime.fromtimestamp(value / 1000)
                else:  # 秒时间戳
                    dt = datetime.fromtimestamp(value)
                return dt.strftime('%Y-%m-%d')
            elif isinstance(value, str):
                # 尝试解析并重新格式化
                for fmt in ['%Y-%m-%d', '%Y-%m-%d %H:%M:%S', '%Y/%m/%d', '%Y%m%d']:
                    try:
                        dt = datetime.strptime(value, fmt)
                        return dt.strftime('%Y-%m-%d')
                    except ValueError:
                        continue
                return value
            elif isinstance(value, datetime):
                return value.strftime('%Y-%m-%d')
            else:
                return str(value)
        except Exception as e:
            logging.error(f"日期转换失败: {value}, 错误: {str(e)}")
            return None
    
    def _convert_text(self, value: Any) -> str:
        """转换文本类型"""
        if value is None:
            return ''
        return str(value).strip()
    
    def map_mysql_to_yida(self, mysql_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        将MySQL数据映射为宜搭格式
        
        Args:
            mysql_data: MySQL数据字典
            
        Returns:
            宜搭格式的数据字典
        """
        yida_data = {}
        
        for mysql_field, value in mysql_data.items():
            yida_field = self.get_yida_field(mysql_field)
            if yida_field:
                converted_value = self.convert_value(mysql_field, value, 'yida')
                yida_data[yida_field] = converted_value
        
        return yida_data
    
    def map_yida_to_mysql(self, yida_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        将宜搭数据映射为MySQL格式
        
        Args:
            yida_data: 宜搭数据字典
            
        Returns:
            MySQL格式的数据字典
        """
        mysql_data = {}
        
        for yida_field, value in yida_data.items():
            mysql_field = self.get_mysql_field(yida_field)
            if mysql_field:
                converted_value = self.convert_value(mysql_field, value, 'mysql')
                mysql_data[mysql_field] = converted_value
        
        return mysql_data
    
    def get_compare_fields_yida(self) -> List[str]:
        """获取需要比较的宜搭字段列表"""
        return [self.get_yida_field(field) for field in self.compare_fields 
                if self.get_yida_field(field)]
    
    def compare_values(self, field: str, mysql_value: Any, yida_value: Any) -> bool:
        """
        比较两个字段值是否相等
        
        Args:
            field: 字段名
            mysql_value: MySQL值
            yida_value: 宜搭值
            
        Returns:
            是否相等
        """
        # 转换值到相同格式进行比较
        converted_mysql = self.convert_value(field, mysql_value, 'yida')
        converted_yida = self.convert_value(field, yida_value, 'yida')
        
        return converted_mysql == converted_yida
    
    def validate_mapping(self) -> List[str]:
        """验证字段映射的有效性"""
        errors = []
        
        # 检查映射是否为空
        if not self.field_mapping:
            errors.append("字段映射不能为空")
            return errors
        
        # 检查比较字段是否都在映射中
        for field in self.compare_fields:
            if field not in self.field_mapping:
                errors.append(f"比较字段 '{field}' 不在字段映射中")
        
        # 检查映射值是否重复
        yida_fields = list(self.field_mapping.values())
        if len(yida_fields) != len(set(yida_fields)):
            errors.append("宜搭字段映射值存在重复")
        
        return errors
    
    def get_mapping_summary(self) -> Dict[str, Any]:
        """获取映射摘要信息"""
        return {
            'total_fields': len(self.field_mapping),
            'compare_fields': len(self.compare_fields),
            'field_types': self.field_types,
            'mapping': self.field_mapping
        } 