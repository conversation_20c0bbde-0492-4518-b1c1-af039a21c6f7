# -*- coding: utf-8 -*-
"""
数据处理类

处理数据转换、分组、比较、验证等操作。
"""

from typing import Dict, List, Any, Optional, Tuple, Set, Union
from datetime import datetime, timedelta
import logging
from .field_mapper import FieldMapper


class DataProcessor:
    """数据处理器"""
    
    def __init__(self, field_mapper: FieldMapper, config):
        """
        初始化数据处理器
        
        Args:
            field_mapper: 字段映射器
            config: 同步配置
        """
        self.field_mapper = field_mapper
        self.config = config
        self.logger = logging.getLogger(__name__)
    
    def process_mysql_data(self, mysql_data: List[Dict[str, Any]]) -> Dict[str, Dict[str, Any]]:
        """
        处理MySQL数据，转换为标准格式并分组
        
        Args:
            mysql_data: MySQL原始数据列表
            
        Returns:
            处理后的数据字典 {unique_key: data}
        """
        processed_data = {}
        
        for item in mysql_data:
            try:
                # 生成唯一标识
                unique_key = self.config.get_unique_key(item)
                if not unique_key:
                    self.logger.warning(f"无法生成唯一标识，跳过数据: {item}")
                    continue
                
                # 转换数值类型
                processed_item = self._convert_data_types(item)
                
                # 存储处理后的数据
                processed_data[unique_key] = processed_item
                
            except Exception as e:
                self.logger.error(f"处理MySQL数据项失败: {str(e)}, 数据: {item}")
                continue
        
        self.logger.info(f"MySQL数据处理完成，共处理 {len(processed_data)} 条记录")
        return processed_data
    
    def process_yida_data(self, yida_data: List[Dict[str, Any]]) -> Dict[str, Dict[str, Any]]:
        """
        处理宜搭数据，转换为标准格式并分组
        
        Args:
            yida_data: 宜搭原始数据列表
            
        Returns:
            处理后的数据字典 {unique_key: data}
        """
        processed_data = {}
        
        for item in yida_data:
            try:
                form_data = item.get('formData', {})
                form_instance_id = item.get('formInstanceId', '')
                
                # 将宜搭数据转换为MySQL格式
                mysql_format_data = self.field_mapper.map_yida_to_mysql(form_data)
                
                # 生成唯一标识
                unique_key = self.config.get_unique_key(mysql_format_data)
                if not unique_key:
                    self.logger.warning(f"无法生成唯一标识，跳过数据: {form_data}")
                    continue
                
                # 添加表单实例ID
                mysql_format_data['_form_instance_id'] = form_instance_id
                
                # 转换数值类型
                processed_item = self._convert_data_types(mysql_format_data)
                
                # 存储处理后的数据
                processed_data[unique_key] = processed_item
                
            except Exception as e:
                self.logger.error(f"处理宜搭数据项失败: {str(e)}, 数据: {item}")
                continue
        
        self.logger.info(f"宜搭数据处理完成，共处理 {len(processed_data)} 条记录")
        return processed_data
    
    def _convert_data_types(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """转换数据类型"""
        converted_data = data.copy()
        
        for field, value in data.items():
            if field.startswith('_'):
                continue  # 跳过内部字段
                
            field_type = self.field_mapper.get_field_type(field)
            
            if field_type == 'number':
                converted_data[field] = self._convert_number(value)
            elif field_type == 'date':
                converted_data[field] = self._convert_date(value)
            elif field_type == 'text':
                converted_data[field] = self._convert_text(value)
        
        return converted_data
    
    def _convert_number(self, value: Any) -> Union[int, float]:
        """转换数值类型"""
        if value is None or value == '':
            return 0
        
        try:
            if isinstance(value, (int, float)):
                return value
            elif isinstance(value, str):
                cleaned = value.replace('¥', '').replace(',', '').replace(' ', '')
                if '.' in cleaned:
                    return float(cleaned)
                else:
                    return int(cleaned)
            else:
                return float(value)
        except (ValueError, TypeError):
            self.logger.warning(f"无法转换数值: {value}，使用默认值0")
            return 0
    
    def _convert_date(self, value: Any) -> str:
        """转换日期类型"""
        if value is None or value == '':
            return None
        
        try:
            if isinstance(value, (int, float)):
                # 时间戳转换
                if value > 1000000000000:  # 毫秒时间戳
                    dt = datetime.fromtimestamp(value / 1000)
                else:  # 秒时间戳
                    dt = datetime.fromtimestamp(value)
                return dt.strftime('%Y-%m-%d')
            elif isinstance(value, str):
                # 尝试解析并重新格式化
                for fmt in ['%Y-%m-%d', '%Y-%m-%d %H:%M:%S', '%Y/%m/%d', '%Y%m%d']:
                    try:
                        dt = datetime.strptime(value, fmt)
                        return dt.strftime('%Y-%m-%d')
                    except ValueError:
                        continue
                return value
            elif isinstance(value, datetime):
                return value.strftime('%Y-%m-%d')
            else:
                return str(value)
        except Exception as e:
            self.logger.error(f"日期转换失败: {value}, 错误: {str(e)}")
            return None
    
    def _convert_text(self, value: Any) -> str:
        """转换文本类型"""
        if value is None:
            return ''
        return str(value).strip()
    
    def compare_data(self, mysql_data: Dict[str, Dict[str, Any]], 
                    yida_data: Dict[str, Dict[str, Any]]) -> Tuple[List[Dict], List[Dict], List[str]]:
        """
        比较MySQL和宜搭数据，找出需要更新和插入的数据
        
        Args:
            mysql_data: MySQL数据字典
            yida_data: 宜搭数据字典
            
        Returns:
            (需要更新的数据列表, 需要插入的数据列表, 错误记录列表)
        """
        update_list = []
        insert_list = []
        error_list = []
        
        # 获取所有唯一键
        all_keys = set(mysql_data.keys()) | set(yida_data.keys())
        
        for key in all_keys:
            try:
                mysql_item = mysql_data.get(key)
                yida_item = yida_data.get(key)
                
                if mysql_item and yida_item:
                    # 数据存在，需要比较
                    if self._need_update(mysql_item, yida_item):
                        update_list.append({
                            'key': key,
                            'mysql_data': mysql_item,
                            'yida_data': yida_item,
                            'form_instance_id': yida_item.get('_form_instance_id', '')
                        })
                elif mysql_item and not yida_item:
                    # MySQL有数据，宜搭没有，需要插入
                    insert_list.append({
                        'key': key,
                        'mysql_data': mysql_item
                    })
                elif not mysql_item and yida_item:
                    # 宜搭有数据，MySQL没有，记录错误
                    error_list.append(f"宜搭数据在MySQL中不存在: {key}")
                else:
                    # 都不存在，跳过
                    continue
                    
            except Exception as e:
                error_list.append(f"比较数据失败: {key}, 错误: {str(e)}")
                continue
        
        self.logger.info(f"数据比较完成 - 更新: {len(update_list)} 条, 插入: {len(insert_list)} 条, 错误: {len(error_list)} 条")
        return update_list, insert_list, error_list
    
    def _need_update(self, mysql_item: Dict[str, Any], yida_item: Dict[str, Any]) -> bool:
        """判断是否需要更新"""
        for field in self.config.compare_fields:
            mysql_value = mysql_item.get(field)
            yida_value = yida_item.get(field)
            
            if not self.field_mapper.compare_values(field, mysql_value, yida_value):
                self.logger.debug(f"字段 {field} 需要更新 - MySQL: {mysql_value}, 宜搭: {yida_value}")
                return True
        
        return False
    
    def group_by_date(self, data: Dict[str, Dict[str, Any]]) -> Dict[str, List[str]]:
        """
        按日期分组数据
        
        Args:
            data: 数据字典
            
        Returns:
            按日期分组的数据 {date: [keys]}
        """
        date_groups = {}
        
        for key, item in data.items():
            date_value = self.config.get_date_value(item)
            if date_value:
                if date_value not in date_groups:
                    date_groups[date_value] = []
                date_groups[date_value].append(key)
        
        return date_groups
    
    def build_search_condition(self, date: str, date_format: str = '%Y-%m-%d') -> List[Dict[str, Any]]:
        """
        构建宜搭查询条件
        
        Args:
            date: 日期字符串
            date_format: 日期格式
            
        Returns:
            查询条件列表
        """
        if not self.config.date_field:
            return []
        
        try:
            # 解析日期
            date_obj = datetime.strptime(date, date_format)
            
            # 计算时间戳范围
            start_timestamp = int(date_obj.timestamp() * 1000)
            
            if date_format == '%Y-%m-%d':
                # 日数据，结束时间为当天23:59:59
                end_timestamp = start_timestamp + 86399000
            elif date_format == '%Y-%m':
                # 月数据，结束时间为下月第一天减1秒
                if date_obj.month == 12:
                    next_month = date_obj.replace(year=date_obj.year + 1, month=1)
                else:
                    next_month = date_obj.replace(month=date_obj.month + 1)
                end_timestamp = int((next_month - timedelta(seconds=1)).timestamp() * 1000)
            else:
                # 其他格式，使用默认的24小时
                end_timestamp = start_timestamp + 86399000
            
            yida_date_field = self.field_mapper.get_yida_field(self.config.date_field)
            if not yida_date_field:
                return []
            
            return [{
                "key": yida_date_field,
                "value": [start_timestamp, end_timestamp],
                "type": "DOUBLE",
                "operator": "between",
                "componentName": "DateField"
            }]
            
        except ValueError as e:
            self.logger.error(f"日期格式转换错误: {date}, {str(e)}")
            return []
    
    def validate_data(self, data: Dict[str, Any]) -> List[str]:
        """
        验证数据有效性
        
        Args:
            data: 数据字典
            
        Returns:
            错误信息列表
        """
        errors = []
        
        # 检查必填字段
        for field in self.config.primary_key_fields:
            if field not in data or not data[field]:
                errors.append(f"必填字段 '{field}' 为空")
        
        # 检查数值字段
        for field in self.config.compare_fields:
            if field in data:
                field_type = self.field_mapper.get_field_type(field)
                if field_type == 'number':
                    try:
                        value = data[field]
                        if value is not None and value != '':
                            float(value)
                    except (ValueError, TypeError):
                        errors.append(f"数值字段 '{field}' 格式错误: {data[field]}")
        
        return errors
    
    def get_statistics(self, mysql_data: Dict[str, Dict[str, Any]], 
                      yida_data: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """
        获取数据统计信息
        
        Args:
            mysql_data: MySQL数据
            yida_data: 宜搭数据
            
        Returns:
            统计信息字典
        """
        mysql_keys = set(mysql_data.keys())
        yida_keys = set(yida_data.keys())
        
        return {
            'mysql_count': len(mysql_data),
            'yida_count': len(yida_data),
            'common_count': len(mysql_keys & yida_keys),
            'mysql_only_count': len(mysql_keys - yida_keys),
            'yida_only_count': len(yida_keys - mysql_keys),
            'total_unique_keys': len(mysql_keys | yida_keys)
        } 